﻿.ax_default {
  font-family:"微软雅黑", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
.box_1 {
}
.paragraph {
  text-align:left;
}
.heading_1 {
  font-family:"微软雅黑 Bold", "微软雅黑", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.horizontal_line {
}
.shape {
}
.box_2 {
}
.ellipse {
}
._图片 {
}
._一级标题 {
  font-family:"微软雅黑", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._二级标题 {
  font-family:"微软雅黑", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._三级标题 {
  font-family:"微软雅黑", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._四级标题 {
  font-family:"微软雅黑", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._五级标题 {
  font-family:"微软雅黑", sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._六级标题 {
  font-family:"微软雅黑", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
._段落 {
  text-align:left;
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
._流程形状 {
}
.icon {
}
.line {
}
.text_field {
  color:#000000;
  text-align:left;
}
.sticky_1 {
  text-align:left;
}
.form_hint {
  color:#999999;
}
.image {
  color:#000000;
}
.placeholder {
}
.iconfont {
  font-family:"iconfont", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#666666;
}
._图片1 {
  color:#000000;
}
.label1 {
  font-size:13px;
  text-align:left;
}
.box_3 {
}
._形状 {
}
.button {
}
.image1 {
}
.refs-webm-wechat {
  font-family:"PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
._段落1 {
  text-align:left;
}
._一级标题1 {
  font-family:"微软雅黑 Bold", "微软雅黑", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._形状1 {
}
.line1 {
}
._形状2 {
}
.label2 {
  font-size:14px;
  text-align:left;
}
.box_11 {
  font-family:"微软雅黑", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
.radio_button {
  text-align:left;
}
.table_cell {
}
._连线 {
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
}
.form_disabled {
}
.ellipse1 {
}
.flow_shape {
}
._默认样式 {
}
.checkbox {
  text-align:left;
}
.image2 {
  color:#000000;
}
._默认样式1 {
}
.box_31 {
}
._36号字_顶部标题、大按钮、弹窗提示主标题_ {
  font-family:"PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.icon1 {
}
._形状3 {
}
.refs-chart-data {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
}
._文本链接 {
  color:#0000FF;
}
textarea, select, input, button { outline: none; }
