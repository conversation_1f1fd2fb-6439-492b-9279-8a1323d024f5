# REITs 项目

基于 Vue 3 + TypeScript + Vite 构建的 REITs 平台前端项目。

## 功能特性

- 🎨 现代化登录界面设计
- 📊 完整的全局仪表盘数据展示
- 📱 响应式布局，支持移动端
- 🔐 完整的登录表单验证
- 🎯 基于 Vue 3 Composition API
- 📈 使用 ECharts 实现数据可视化
- ⚡ 使用 Vite 构建，开发体验优秀

## 页面功能

### 登录页面

项目包含一个参照 MasterGo 设计的登录页面，具有以下特点：

- **品牌展示**：左上角显示 logo 和 "rev-REITs平台" 标题
- **登录方式**：支持密码登录和短信登录切换
- **表单验证**：完整的用户名、密码输入验证
- **用户协议**：必须同意用户协议才能登录
- **第三方登录**：支持中国REITs论坛账号登录
- **响应式设计**：适配不同屏幕尺寸

### 全局仪表盘

项目包含一个严格按照 MasterGo 设计的全局仪表盘页面，具有以下特点：

- **数据可视化**：使用 ECharts 实现专业的图表展示
- **市场概览**：公募REITs市场趋势折线图
- **排行榜**：REITs涨幅TOP3实时展示
- **行业分析**：分行业市值占比饼图
- **地域分布**：资产地域分布进度条展示
- **信息模块**：行业日历、市场新闻、宏观数据、Cap Rate
- **响应式设计**：适配桌面端和移动端

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

访问 http://localhost:3000 查看登录页面，登录成功后自动跳转到全局仪表盘页面。

### 构建生产版本

```bash
pnpm build
```

### 类型检查

```bash
pnpm type-check
```

### 预览构建结果

```bash
pnpm preview
```

## 项目结构

```
src/
├── views/
│   ├── Login.vue          # 登录页面
│   ├── Home.vue           # 首页
│   ├── Dashboard.vue      # 仪表盘
│   ├── Charts.vue         # 图表页面
│   └── Table.vue          # 表格页面
├── router/
│   └── index.ts           # 路由配置
├── components/            # 公共组件
├── assets/               # 静态资源
└── styles/               # 全局样式
```

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 下一代前端构建工具
- **Vue Router** - Vue.js 官方路由管理器
- **UnoCSS** - 即时原子化 CSS 引擎
- **Ant Design Vue** - 企业级 UI 组件库

## 开发说明

### 登录页面路由

- `/` - 默认登录页面
- `/login` - 登录页面
- `/home` - 首页（登录后跳转）
- `/dashboard` - 仪表盘页面

### 登录功能

登录页面包含以下功能：

1. **表单验证**：用户名和密码必填
2. **协议同意**：必须勾选用户协议
3. **登录状态**：显示登录中状态
4. **错误处理**：登录失败提示
5. **路由跳转**：登录成功后跳转到仪表盘

### 样式特点

- 使用 CSS Grid 和 Flexbox 布局
- 半透明背景效果
- 圆角设计和阴影效果
- 蓝色主题色 (#1868F1)
- 响应式设计适配移动端

## 许可证

MIT License 