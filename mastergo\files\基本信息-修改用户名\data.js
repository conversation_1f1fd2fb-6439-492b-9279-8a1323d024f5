﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,m)),n,[],o,_(h,p),q,[r],s,_(t,u,v,w,g,x,y,_(),z,[],A,_(B,C,D,E,F,_(G,H,I,J),K,null,L,_(M,N,O,N),P,Q,R,null,S,T,U,V,W,X,Y,T,Z,ba,bb,_(G,H,I,bc),bd,T,be,ba,bf,_(bg,bh,bi,bj,bk,bj,bl,bj,bm,m,I,_(bn,bo,bp,bo,bq,bo,br,bs)),i,_(j,m,l,m)),bt,_(),bu,_(),bv,_(bw,[_(bx,by,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,bG,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),i,_(j,k,l,bR),B,bS,F,_(G,H,I,bO),bP,bT),bt,_(),bU,_(),bV,bh,bW,bh,bX,bh),_(bx,bY,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,bG,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),i,_(j,bZ,l,ca),B,bS,cb,_(cc,cd,ce,cf),F,_(G,H,I,J),bd,cg),bt,_(),bU,_(),bV,bh,bW,bh,bX,bh),_(bx,ch,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),i,_(j,cj,l,ck),B,cl,cb,_(cc,cm,ce,cn),co,cp,cq,cr),bt,_(),bU,_(),bV,bh,bW,bF,bX,bF),_(bx,cs,bz,h,bA,ct,v,cu,bD,cu,bE,bF,A,_(W,bG,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),B,cv,i,_(j,cw,l,cw),cb,_(cc,cx,ce,cy),K,null),bt,_(),bU,_(),bu,_(cz,_(cA,cB,cC,cD,cE,[_(cC,h,cF,h,cG,bh,cH,cI,cJ,[_(cK,cL,cC,cM,cN,cO,cP,_(cQ,_(h,cM)),cR,_(cS,cT,cU,bh),cV,cW)])])),cX,bF,cY,_(cZ,da),bW,bh,bX,bh),_(bx,db,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),i,_(j,dc,l,dd),B,de,cb,_(cc,df,ce,dg),cq,dh),bt,_(),bU,_(),bV,bh,bW,bF,bX,bF),_(bx,di,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bN,_(G,H,I,dj,bP,bQ),bH,bI,bJ,bK,bL,bM,i,_(j,dk,l,dl),B,dm,cb,_(cc,dn,ce,dp),bd,bM,bb,_(G,H,I,dq),dr,ds,dt,cg,cq,dh,F,_(G,H,I,J),Y,du),bt,_(),bU,_(),bV,bh,bW,bh,bX,bh),_(bx,dv,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),i,_(j,dc,l,dd),B,de,cb,_(cc,df,ce,dw),cq,dh),bt,_(),bU,_(),bV,bh,bW,bF,bX,bF),_(bx,dx,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bN,_(G,H,I,dj,bP,bQ),bH,bI,bJ,bK,bL,bM,i,_(j,dk,l,dl),B,dm,cb,_(cc,dn,ce,dy),bd,bM,bb,_(G,H,I,dq),dr,ds,dt,cg,cq,dh,F,_(G,H,I,J),Y,du),bt,_(),bU,_(),bV,bh,bW,bh,bX,bh),_(bx,dz,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bN,_(G,H,I,J,bP,bQ),bH,bI,bJ,bK,bL,bM,i,_(j,dA,l,dl),B,dm,cb,_(cc,dB,ce,dC),F,_(G,H,I,dD),bd,dE,bb,_(G,H,I,dj),cq,cr),bt,_(),bU,_(),bu,_(cz,_(cA,cB,cC,cD,cE,[_(cC,h,cF,h,cG,bh,cH,cI,cJ,[_(cK,cL,cC,cM,cN,cO,cP,_(cQ,_(h,cM)),cR,_(cS,cT,cU,bh),cV,cW)])])),cX,bF,bV,bh,bW,bh,bX,bh),_(bx,dF,bz,dG,bA,dH,v,dI,bD,dI,bE,bF,A,_(W,dJ,bH,bI,bJ,bK,bL,bM,bN,_(G,H,I,bO,bP,bQ),cb,_(cc,dK,ce,dL),i,_(j,bQ,l,bQ)),bt,_(),bU,_(),dM,[_(bx,dN,bz,h,bA,bB,v,bC,bD,bC,bE,bF,A,_(W,ci,bN,_(G,H,I,J,bP,bQ),bH,bI,bJ,bK,bL,bM,i,_(j,dO,l,dP),bd,bM,dt,dQ,dR,dQ,dS,dT,Y,T,B,dU,dV,dW,bf,_(bg,bh,bi,m,bk,m,bl,dX,bm,m,I,_(bn,dY,bp,dY,bq,dY,br,bs)),cb,_(cc,dZ,ce,ea),F,_(G,H,I,eb)),bt,_(),bU,_(),bV,bh,bW,bh,bX,bh),_(bx,ec,bz,h,bA,ed,v,bC,bD,bC,bE,bF,A,_(bN,_(G,H,I,J,bP,bQ),W,bG,bH,bI,bJ,bK,bL,bM,cb,_(cc,ee,ce,ef),i,_(j,eg,l,eh),Y,T,B,dU,bf,_(bg,bh,bi,m,bk,m,bl,dX,bm,m,I,_(bn,dY,bp,dY,bq,dY,br,bs)),F,_(G,H,I,eb)),bt,_(),bU,_(),cY,_(cZ,ei),bV,bh,bW,bh,bX,bh)],ej,bh)])),ek,_(),el,_(em,_(en,eo),ep,_(en,eq),er,_(en,es),et,_(en,eu),ev,_(en,ew),ex,_(en,ey),ez,_(en,eA),eB,_(en,eC),eD,_(en,eE),eF,_(en,eG),eH,_(en,eI),eJ,_(en,eK)));}; 
var b="url",c="基本信息-修改用户名.html",d="generationDate",e=new Date(1753156621662.6404),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=1912,l="height",m=0,n="adaptiveViews",o="sketchKeys",p="s0",q="variables",r="OnLoadVariable",s="page",t="packageId",u="db7ae0ec0584433d9f729dc3c37823df",v="type",w="Axure:Page",x="基本信息-修改用户名",y="notes",z="annotations",A="style",B="baseStyle",C="627587b6038d43cca051c114ac41ad32",D="pageAlignment",E="center",F="fill",G="fillType",H="solid",I="color",J=0xFFFFFFFF,K="image",L="imageAlignment",M="horizontal",N="near",O="vertical",P="imageRepeat",Q="auto",R="favicon",S="sketchFactor",T="0",U="colorStyle",V="appliedColor",W="fontName",X="Applied Font",Y="borderWidth",Z="borderVisibility",ba="all",bb="borderFill",bc=0xFF797979,bd="cornerRadius",be="cornerVisibility",bf="outerShadow",bg="on",bh=false,bi="offsetX",bj=5,bk="offsetY",bl="blurRadius",bm="spread",bn="r",bo=0,bp="g",bq="b",br="a",bs=0.34901960784313724,bt="adaptiveStyles",bu="interactionMap",bv="diagram",bw="objects",bx="id",by="a5e7d94e81c946f8b7288bea70e439a3",bz="label",bA="friendlyType",bB="矩形",bC="vectorShape",bD="styleType",bE="visible",bF=true,bG="\"微软雅黑\", sans-serif",bH="fontWeight",bI="400",bJ="fontStyle",bK="normal",bL="fontStretch",bM="5",bN="foreGroundFill",bO=0xFF333333,bP="opacity",bQ=1,bR=954,bS="36ca983ea13942bab7dd1ef3386ceb3e",bT="0.75",bU="imageOverrides",bV="generateCompound",bW="autoFitWidth",bX="autoFitHeight",bY="68926e2964bb49cc8a7550c1953b1cfd",bZ=500,ca=311,cb="location",cc="x",cd=706,ce="y",cf=240,cg="10",ch="847f8cd9c8f746feaa31fbe91c1303e7",ci="\"STHeiti SC Medium\", \"STHeiti SC\", sans-serif",cj=80,ck=16,cl="8c7a4c5ad69a4369a5f7788171ac0b32",cm=726,cn=260,co="verticalAlignment",cp="middle",cq="fontSize",cr="16px",cs="4f812410b5584a61a4b564809e197da6",ct="图片",cu="imageBox",cv="********************************",cw=20,cx=1166,cy=258,cz="onClick",cA="eventType",cB="Click时",cC="description",cD="点击或轻触",cE="cases",cF="conditionString",cG="isNewIfGroup",cH="caseColorHex",cI="AB68FF",cJ="actions",cK="action",cL="linkWindow",cM="在 当前窗口 打开 置于底层",cN="displayName",cO="打开链接",cP="actionInfoDescriptions",cQ="置于底层",cR="target",cS="targetType",cT="backUrl",cU="includeVariables",cV="linkType",cW="current",cX="tabbable",cY="images",cZ="normal~",da="images/公募reits产品及资产-产品对比/u2006.png",db="eec0bff3f2214f63b02be7bfc67bd6bb",dc=56,dd=14,de="f8cddf558f9143b98a34921f8e28acbf",df=746,dg=324,dh="14px",di="9885267c4c4e49709f83264d2d5fc0c6",dj=0xFFAAAAAA,dk=353.72572721112783,dl=50,dm="47641f9a00ac465095d6b672bbdffef6",dn=812,dp=306,dq=0xFF7F7F7F,dr="horizontalAlignment",ds="left",dt="paddingLeft",du="1",dv="b5a7d51a9c9d47bdaa6c36b04b4483a0",dw=394,dx="68250def77c945e38a2f822c626fc0c0",dy=376,dz="703fb6d48a6e4434b25655b067848121",dA=200,dB=856,dC=471,dD=0xFF1868F1,dE="25",dF="95349fa647eb42c8bc049779d1802504",dG="预约",dH="组合",dI="layer",dJ="\"Arial Normal\", \"Arial\", sans-serif",dK=1423.6283783783783,dL=473.6486486486487,dM="objs",dN="3afb81911516474fb16b4a1e7b6bb5d4",dO=197,dP=30,dQ="8",dR="paddingRight",dS="paddingBottom",dT="3",dU="554e399a3fc64ba49bd2c7bd8fff1914",dV="lineSpacing",dW="18px",dX=10,dY=85,dZ=822,ea=356,eb=0xBF000000,ec="ef100ada1ec6489993cd999ff1fe0231",ed="Triangle Down",ee=915,ef=386,eg=11,eh=6,ei="images/注册/u81.svg",ej="propagate",ek="masters",el="objectPaths",em="a5e7d94e81c946f8b7288bea70e439a3",en="scriptId",eo="u3200",ep="68926e2964bb49cc8a7550c1953b1cfd",eq="u3201",er="847f8cd9c8f746feaa31fbe91c1303e7",es="u3202",et="4f812410b5584a61a4b564809e197da6",eu="u3203",ev="eec0bff3f2214f63b02be7bfc67bd6bb",ew="u3204",ex="9885267c4c4e49709f83264d2d5fc0c6",ey="u3205",ez="b5a7d51a9c9d47bdaa6c36b04b4483a0",eA="u3206",eB="68250def77c945e38a2f822c626fc0c0",eC="u3207",eD="703fb6d48a6e4434b25655b067848121",eE="u3208",eF="95349fa647eb42c8bc049779d1802504",eG="u3209",eH="3afb81911516474fb16b4a1e7b6bb5d4",eI="u3210",eJ="ef100ada1ec6489993cd999ff1fe0231",eK="u3211";
return _creator();
})());