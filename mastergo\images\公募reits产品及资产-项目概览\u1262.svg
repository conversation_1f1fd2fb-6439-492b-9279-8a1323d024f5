﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="165px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="437px" y="330px" width="165px" height="79px" filterUnits="userSpaceOnUse" id="filter24">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget25">
      <path d="M 447 344  A 4 4 0 0 1 451 340 L 588 340  A 4 4 0 0 1 592 344 L 592 395  A 4 4 0 0 1 588 399 L 451 399  A 4 4 0 0 1 447 395 L 447 344  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -437 -330 )">
    <use xlink:href="#widget25" filter="url(#filter24)" />
    <use xlink:href="#widget25" />
  </g>
</svg>