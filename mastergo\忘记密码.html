﻿<!DOCTYPE html>
<html>
  <head>
    <title>忘记密码</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/忘记密码/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/忘记密码/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u87" class="ax_default box_2">
        <div id="u87_div" class=""></div>
        <div id="u87_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u88" class="ax_default _图片">
        <img id="u88_img" class="img " src="images/登录-密码登录/u1.png"/>
        <div id="u88_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u89" class="ax_default box_2">
        <div id="u89_div" class=""></div>
        <div id="u89_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u90" class="ax_default" data-left="40" data-top="40" data-width="195" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u91" class="ax_default _三级标题">
          <div id="u91_div" class=""></div>
          <div id="u91_text" class="text ">
            <p><span>rev-REITs平台</span></p>
          </div>
        </div>

        <!-- Unnamed (圆形) -->
        <div id="u92" class="ax_default ellipse">
          <img id="u92_img" class="img " src="images/登录-密码登录/u5.svg"/>
          <div id="u92_text" class="text ">
            <p><span>logo</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u93" class="ax_default box_2">
        <div id="u93_div" class=""></div>
        <div id="u93_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u94" class="ax_default _三级标题">
        <div id="u94_div" class=""></div>
        <div id="u94_text" class="text ">
          <p><span>找回密码</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u95" class="ax_default" data-left="1627" data-top="254" data-width="75" data-height="16" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u96" class="ax_default _三级标题">
          <div id="u96_div" class=""></div>
          <div id="u96_text" class="text ">
            <p><span>返回</span><span style="color:#1868F1;">登录</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u97" class="ax_default icon">
          <img id="u97_img" class="img " src="images/登录-密码登录/u10.svg"/>
          <div id="u97_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u98" class="ax_default _三级标题">
        <div id="u98_div" class=""></div>
        <div id="u98_text" class="text ">
          <p><span>手&nbsp; 机&nbsp; 号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u99" class="ax_default box_2">
        <div id="u99_div" class=""></div>
        <div id="u99_text" class="text ">
          <p><span>请输入绑定的手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u100" class="ax_default _三级标题">
        <div id="u100_div" class=""></div>
        <div id="u100_text" class="text ">
          <p><span>新&nbsp; 密&nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u101" class="ax_default box_2">
        <div id="u101_div" class=""></div>
        <div id="u101_text" class="text ">
          <p><span>请设置登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u102" class="ax_default _三级标题">
        <div id="u102_div" class=""></div>
        <div id="u102_text" class="text ">
          <p><span>确认密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u103" class="ax_default box_2">
        <div id="u103_div" class=""></div>
        <div id="u103_text" class="text ">
          <p><span>请再次确认登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u104" class="ax_default _三级标题">
        <div id="u104_div" class=""></div>
        <div id="u104_text" class="text ">
          <p><span>验&nbsp; 证&nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u105" class="ax_default box_2">
        <div id="u105_div" class=""></div>
        <div id="u105_text" class="text ">
          <p><span>请输入验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u106" class="ax_default box_2">
        <div id="u106_div" class=""></div>
        <div id="u106_text" class="text ">
          <p><span>获取验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u107" class="ax_default box_2">
        <div id="u107_div" class=""></div>
        <div id="u107_text" class="text ">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- 预约 (组合) -->
      <div id="u108" class="ax_default" data-label="预约" data-left="1286" data-top="431" data-width="290" data-height="36" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u109" class="ax_default _形状1">
          <div id="u109_div" class=""></div>
          <div id="u109_text" class="text ">
            <p><span>6-20个字符，需包含字母、数字，不能包含空格</span></p>
          </div>
        </div>

        <!-- Unnamed (Triangle Down) -->
        <div id="u110" class="ax_default _形状1">
          <img id="u110_img" class="img " src="images/注册/u84.svg"/>
          <div id="u110_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u111" class="ax_default _图片">
        <img id="u111_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u111_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u112" class="ax_default _图片">
        <img id="u112_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u112_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u113" class="ax_default ax_default_hidden" style="display:none; visibility: hidden" data-left="1337" data-top="495" data-width="230" data-height="50" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u114" class="ax_default _形状1">
          <div id="u114_div" class=""></div>
          <div id="u114_text" class="text ">
            <p><span>修改成功，即将跳转登录页面</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u115" class="ax_default icon">
          <img id="u115_img" class="img " src="images/忘记密码/u115.svg"/>
          <div id="u115_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
