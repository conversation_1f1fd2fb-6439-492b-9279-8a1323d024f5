<template>
  <div class="login-container">
    <!-- 背景图片 -->
    <div class="background-image"></div>
    
    <!-- 半透明遮罩 -->
    <div class="overlay"></div>
    
    <!-- 登录内容 -->
    <div class="login-content">
      <!-- 左上角品牌区域 -->
      <div class="brand-section">
        <div class="logo">
          <div class="logo-icon"></div>
        </div>
        <div class="brand-title">rev-REITs平台</div>
      </div>
      
      <!-- 登录框 -->
      <div class="login-box">
        <!-- 登录方式切换 -->
        <div class="login-tabs">
          <div 
            class="tab-item" 
            :class="{ active: loginType === 'password' }"
            @click="loginType = 'password'"
          >
            密码登录
          </div>
          <div 
            class="tab-item" 
            :class="{ active: loginType === 'sms' }"
            @click="loginType = 'sms'"
          >
            短信登录
          </div>
        </div>
        
        <!-- 注册链接 -->
        <div class="register-link">
          <span>还没有账号，去</span>
          <a href="#" class="link">注册</a>
          <span class="arrow">></span>
        </div>
        
        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="login-form">
          <!-- 用户名/手机号输入 -->
          <div class="form-group">
            <label class="form-label">{{ loginType === 'password' ? '用户名' : '手机号' }}</label>
            <input
              v-model="formData.username"
              type="text"
              class="form-input"
              :placeholder="loginType === 'password' ? '请输入用户名/手机号' : '请输入手机号'"
              required
            />
          </div>
          
          <!-- 密码输入 -->
          <div v-if="loginType === 'password'" class="form-group">
            <label class="form-label">密码</label>
            <div class="password-input-container">
              <input
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                placeholder="请输入登录密码"
                required
              />
              <button
                type="button"
                class="password-toggle"
                @click="showPassword = !showPassword"
              >
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
          </div>
          
          <!-- 短信验证码输入 -->
          <div v-if="loginType === 'sms'" class="form-group">
            <label class="form-label">验证码</label>
            <div class="sms-input-container">
              <input
                v-model="formData.smsCode"
                type="text"
                class="form-input"
                placeholder="请输入验证码"
                required
              />
              <button
                type="button"
                class="sms-button"
                :disabled="smsCountdown > 0"
                @click="sendSmsCode"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </button>
            </div>
          </div>
          
          <!-- 协议复选框 -->
          <div class="agreement-section">
            <label class="checkbox-container">
              <input
                v-model="formData.agreement"
                type="checkbox"
                class="checkbox"
                required
              />
              <span class="checkmark"></span>
              <span class="agreement-text">
                阅读并同意
                <a href="#" class="link">用户协议</a>、
                <a href="#" class="link">隐私声明</a>、
                <a href="#" class="link">产品使用条款</a>
              </span>
            </label>
          </div>
          
          <!-- 登录按钮 -->
          <button
            type="submit"
            class="login-button"
            :disabled="!formData.agreement || loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </form>
        
        <!-- 忘记密码 -->
        <div v-if="loginType === 'password'" class="forgot-password">
          <a href="#" class="link">忘记密码</a>
        </div>
        
        <!-- 分割线 -->
        <div class="divider">
          <span class="divider-text">其他登录方式</span>
        </div>
        
        <!-- 第三方登录 -->
        <div class="third-party-login">
          <button class="third-party-button" @click="handleThirdPartyLogin">
            中国REITs论坛账号登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)
const loginType = ref<'password' | 'sms'>('password')
const showPassword = ref(false)
const smsCountdown = ref(0)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  smsCode: '',
  agreement: false
})

// 发送短信验证码
const sendSmsCode = async () => {
  if (!formData.username) {
    alert('请先输入手机号')
    return
  }
  
  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(formData.username)) {
    alert('请输入正确的手机号')
    return
  }
  
  try {
    // 模拟发送短信验证码
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
    alert('验证码已发送')
  } catch (error) {
    console.error('发送验证码失败:', error)
    alert('发送验证码失败，请重试')
  }
}

// 登录处理
const handleLogin = async () => {
  if (!formData.username) {
    alert(loginType.value === 'password' ? '请输入用户名' : '请输入手机号')
    return
  }
  
  if (loginType.value === 'password' && !formData.password) {
    alert('请输入密码')
    return
  }
  
  if (loginType.value === 'sms' && !formData.smsCode) {
    alert('请输入验证码')
    return
  }
  
  if (!formData.agreement) {
    alert('请阅读并同意用户协议')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 登录成功，跳转到首页
    router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 第三方登录
const handleThirdPartyLogin = () => {
  alert('跳转到中国REITs论坛进行登录验证')
  // 这里可以添加实际的第三方登录逻辑
}
</script>

<style scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
}

.login-content {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.brand-section {
  position: absolute;
  top: 30px;
  left: 40px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  width: 40px;
  height: 40px;
  background-color: #1868F1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
}

.brand-title {
  font-size: 24px;
  font-weight: 500;
  color: #1868F1;
  font-family: 'STHeiti SC Medium', 'STHeiti SC', sans-serif;
}

.login-box {
  position: absolute;
  right: 180px;
  top: 50%;
  transform: translateY(-50%);
  width: 560px;
  height: 551px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 40px;
  box-sizing: border-box;
}

.login-tabs {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
}

.tab-item {
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 10px 0;
  position: relative;
  transition: color 0.3s;
}

.tab-item:hover {
  color: #1868F1;
}

.tab-item.active {
  color: #1868F1;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #1868F1;
  border-radius: 2px;
}

.register-link {
  position: absolute;
  top: 42px;
  right: 40px;
  font-size: 16px;
  color: #666;
}

.register-link .link {
  color: #1868F1;
  text-decoration: none;
  margin: 0 5px;
}

.register-link .arrow {
  color: #1868F1;
  margin-left: 5px;
}

.login-form {
  margin-top: 60px;
}

.form-group {
  margin-bottom: 30px;
}

.form-label {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
  text-align: left;
}

.form-input {
  width: 100%;
  height: 50px;
  padding: 0 15px;
  border: 1px solid #d7d7d7;
  border-radius: 5px;
  font-size: 16px;
  color: #333;
  background-color: white;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: #aaa;
}

.form-input:focus {
  outline: none;
  border-color: #1868F1;
}

.password-input-container,
.sms-input-container {
  position: relative;
}

.password-toggle,
.sms-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  padding: 5px;
}

.sms-button {
  background-color: #1868F1;
  color: white;
  border-radius: 5px;
  padding: 8px 15px;
  font-size: 14px;
  min-width: 100px;
}

.sms-button:hover:not(:disabled) {
  background-color: #1557d1;
}

.sms-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.agreement-section {
  margin-bottom: 20px;
}

.checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
}

.checkbox {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 1px solid #d7d7d7;
  border-radius: 2px;
  background-color: white;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox:checked + .checkmark {
  background-color: #1868F1;
  border-color: #1868F1;
}

.checkbox:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
}

.agreement-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.agreement-text .link {
  color: #1868F1;
  text-decoration: none;
}

.login-button {
  width: 100%;
  height: 50px;
  background-color: #1868F1;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-button:hover:not(:disabled) {
  background-color: #1557d1;
}

.login-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.forgot-password {
  text-align: center;
  margin-top: 20px;
}

.forgot-password .link {
  color: #1868F1;
  text-decoration: none;
  font-size: 16px;
}

.divider {
  position: relative;
  text-align: center;
  margin: 40px 0 20px;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #d7d7d7;
}

.divider-text {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0 20px;
  color: #aaa;
  font-size: 16px;
}

.third-party-login {
  text-align: center;
}

.third-party-button {
  background: none;
  border: none;
  color: #7f7f7f;
  font-size: 16px;
  cursor: pointer;
  padding: 10px 20px;
  transition: color 0.3s;
}

.third-party-button:hover {
  color: #1868F1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .login-box {
    right: 50px;
    width: 500px;
  }
}

@media (max-width: 768px) {
  .login-box {
    position: relative;
    right: auto;
    top: auto;
    transform: none;
    width: 90%;
    max-width: 400px;
    margin: 100px auto 0;
  }
  
  .brand-section {
    position: relative;
    top: 20px;
    left: 20px;
  }
}
</style> 