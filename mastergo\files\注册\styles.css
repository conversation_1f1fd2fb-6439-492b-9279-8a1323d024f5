﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u53_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u53 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u54_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u54 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u55_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.4980392156862745);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u55 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u55_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u57_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#1868F1;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:48px;
  width:145px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#1868F1;
}
#u57 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u57_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u58_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:40px;
  width:40px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u58 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u58_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u59_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:560px;
  height:591px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.8);
  border:none;
  border-radius:20px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:1172px;
  top:181px;
  width:560px;
  height:591px;
  display:flex;
}
#u59 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u59_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u60_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:1212px;
  top:221px;
  width:112px;
  height:29px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u60 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u60_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u62_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:1563px;
  top:228px;
  width:128px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u62 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u62_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u63_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:1696px;
  top:231px;
  width:6px;
  height:10px;
  display:flex;
}
#u63 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u63_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u64_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:307px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u64 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u64_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u65_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:290px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u65 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u65_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u66_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:447px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u66 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u66_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u67_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 0, 27, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:430px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u67 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u67_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u68_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:517px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u68 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u68_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u69_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 0, 27, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:500px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u69 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u69_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u70_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:587px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u70 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u71_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:570px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u71 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u71_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u72_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:377px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u72 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u72_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u73_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:360px;
  width:300px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u73 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u74_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:1586px;
  top:360px;
  width:116px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u74 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u74_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u75_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:682px;
  width:500px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u75 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u77 label {
  left:0px;
  width:100%;
}
#u77_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:660px;
  width:16px;
  height:12px;
  display:flex;
}
#u77 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u77_img.selected {
}
#u77.selected {
}
#u77_img.disabled {
}
#u77.disabled {
}
#u77_img.selected.error {
}
#u77.selected.error {
}
#u77_img.selected.hint {
}
#u77.selected.hint {
}
#u77_img.selected.error.hint {
}
#u77.selected.error.hint {
}
#u77_img.mouseOver.selected {
}
#u77.mouseOver.selected {
}
#u77_img.mouseOver.selected.error {
}
#u77.mouseOver.selected.error {
}
#u77_img.mouseOver.selected.hint {
}
#u77.mouseOver.selected.hint {
}
#u77_img.mouseOver.selected.error.hint {
}
#u77.mouseOver.selected.error.hint {
}
#u77_img.mouseDown.selected {
}
#u77.mouseDown.selected {
}
#u77_img.mouseDown.selected.error {
}
#u77.mouseDown.selected.error {
}
#u77_img.mouseDown.selected.hint {
}
#u77.mouseDown.selected.hint {
}
#u77_img.mouseDown.selected.error.hint {
}
#u77.mouseDown.selected.error.hint {
}
#u77_img.mouseOver.mouseDown.selected {
}
#u77.mouseOver.mouseDown.selected {
}
#u77_img.mouseOver.mouseDown.selected.error {
}
#u77.mouseOver.mouseDown.selected.error {
}
#u77_img.mouseOver.mouseDown.selected.hint {
}
#u77.mouseOver.mouseDown.selected.hint {
}
#u77_img.mouseOver.mouseDown.selected.error.hint {
}
#u77.mouseOver.mouseDown.selected.error.hint {
}
#u77_img.focused.selected {
}
#u77.focused.selected {
}
#u77_img.focused.selected.error {
}
#u77.focused.selected.error {
}
#u77_img.focused.selected.hint {
}
#u77.focused.selected.hint {
}
#u77_img.focused.selected.error.hint {
}
#u77.focused.selected.error.hint {
}
#u77_img.selected.disabled {
}
#u77.selected.disabled {
}
#u77_img.selected.hint.disabled {
}
#u77.selected.hint.disabled {
}
#u77_img.selected.error.disabled {
}
#u77.selected.error.disabled {
}
#u77_img.selected.error.hint.disabled {
}
#u77.selected.error.hint.disabled {
}
#u77_text {
  border-width:0px;
  position:absolute;
  left:12px;
  top:0px;
  width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u77_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u78_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:1218px;
  top:660px;
  width:252px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u78 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u80_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:192px;
  height:30px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.7490196078431373);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:1286px;
  top:405px;
  width:192px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u81_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:6px;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:435px;
  width:11px;
  height:6px;
  display:flex;
  color:#FFFFFF;
}
#u81 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:30px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.7490196078431373);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:1286px;
  top:475px;
  width:275px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u83 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u83_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u84_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:6px;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:505px;
  width:11px;
  height:6px;
  display:flex;
  color:#FFFFFF;
}
#u84 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u84_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u85_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:1662px;
  top:585px;
  width:20px;
  height:20px;
  display:flex;
}
#u85 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u85_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u86_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:1662px;
  top:515px;
  width:20px;
  height:20px;
  display:flex;
}
#u86 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
