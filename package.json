{"name": "reits-project", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@vueuse/core": "^10.7.2", "ant-design-vue": "^4.0.0", "echarts": "^5.4.3", "pinia": "^2.1.7", "qiankun": "^2.10.16", "vue": "^3.4.21", "vue-router": "^4.2.5", "vxe-table": "^4.5.0"}, "devDependencies": {"@types/node": "^24.0.15", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.2.5", "typescript": "~5.3.0", "unocss": "^66.3.3", "vite": "^6.0.0", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}}