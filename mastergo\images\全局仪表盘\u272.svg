﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="170px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="375px" y="655px" width="170px" height="79px" filterUnits="userSpaceOnUse" id="filter8">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget9">
      <path d="M 385 669  A 4 4 0 0 1 389 665 L 531 665  A 4 4 0 0 1 535 669 L 535 720  A 4 4 0 0 1 531 724 L 389 724  A 4 4 0 0 1 385 720 L 385 669  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -375 -655 )">
    <use xlink:href="#widget9" filter="url(#filter8)" />
    <use xlink:href="#widget9" />
  </g>
</svg>