﻿<!DOCTYPE html>
<html>
  <head>
    <title>基本信息-修改用户名</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/基本信息-修改用户名/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/基本信息-修改用户名/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u3200" class="ax_default box_3">
        <div id="u3200_div" class=""></div>
        <div id="u3200_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3201" class="ax_default box_3">
        <div id="u3201_div" class=""></div>
        <div id="u3201_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3202" class="ax_default _三级标题">
        <div id="u3202_div" class=""></div>
        <div id="u3202_text" class="text ">
          <p><span>修改用户名</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u3203" class="ax_default _图片">
        <img id="u3203_img" class="img " src="images/公募reits产品及资产-产品对比/u2006.png"/>
        <div id="u3203_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3204" class="ax_default label1">
        <div id="u3204_div" class=""></div>
        <div id="u3204_text" class="text ">
          <p><span>原用户名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3205" class="ax_default box_2">
        <div id="u3205_div" class=""></div>
        <div id="u3205_text" class="text ">
          <p><span>chsdufizj</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3206" class="ax_default label1">
        <div id="u3206_div" class=""></div>
        <div id="u3206_text" class="text ">
          <p><span>新用户名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3207" class="ax_default box_2">
        <div id="u3207_div" class=""></div>
        <div id="u3207_text" class="text ">
          <p><span>请输入新用户名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3208" class="ax_default box_2">
        <div id="u3208_div" class=""></div>
        <div id="u3208_text" class="text ">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- 预约 (组合) -->
      <div id="u3209" class="ax_default" data-label="预约" data-left="822" data-top="356" data-width="197" data-height="36" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u3210" class="ax_default _形状1">
          <div id="u3210_div" class=""></div>
          <div id="u3210_text" class="text ">
            <p><span>5-12个字符，需包含字母或汉字</span></p>
          </div>
        </div>

        <!-- Unnamed (Triangle Down) -->
        <div id="u3211" class="ax_default _形状1">
          <img id="u3211_img" class="img " src="images/注册/u81.svg"/>
          <div id="u3211_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
