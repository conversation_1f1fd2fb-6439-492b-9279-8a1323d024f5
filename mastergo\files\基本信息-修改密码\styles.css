﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3242 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
  opacity:0.75;
}
#u3242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:326px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3243 {
  border-width:0px;
  position:absolute;
  left:706px;
  top:240px;
  width:500px;
  height:326px;
  display:flex;
}
#u3243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3244 {
  border-width:0px;
  position:absolute;
  left:726px;
  top:260px;
  width:66px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Bold", "STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3244 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3244_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3245_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u3245 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:258px;
  width:20px;
  height:20px;
  display:flex;
}
#u3245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u3246 {
  border-width:0px;
  position:absolute;
  left:856px;
  top:486px;
  width:200px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u3246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:right;
}
#u3247 {
  border-width:0px;
  position:absolute;
  left:754px;
  top:323px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:right;
}
#u3247 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3247_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3248_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:346px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3248 {
  border-width:0px;
  position:absolute;
  left:820px;
  top:306px;
  width:346px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:right;
}
#u3249 {
  border-width:0px;
  position:absolute;
  left:754px;
  top:393px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:right;
}
#u3249 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3249_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:346px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(127, 127, 127, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3250 {
  border-width:0px;
  position:absolute;
  left:820px;
  top:376px;
  width:346px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#AAAAAA;
  text-align:left;
}
#u3250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3251 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3252_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:30px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.7490196078431373);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u3252 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:281px;
  width:290px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u3252 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u3252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3253_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:6px;
}
#u3253 {
  border-width:0px;
  position:absolute;
  left:912px;
  top:311px;
  width:11px;
  height:6px;
  display:flex;
  color:#FFFFFF;
}
#u3253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3254_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u3254 {
  border-width:0px;
  position:absolute;
  left:1126px;
  top:391px;
  width:20px;
  height:20px;
  display:flex;
}
#u3254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3255_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u3255 {
  border-width:0px;
  position:absolute;
  left:1126px;
  top:321px;
  width:20px;
  height:20px;
  display:flex;
}
#u3255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3256 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.7490196078431373);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u3257 {
  border-width:0px;
  position:absolute;
  left:896px;
  top:361px;
  width:120px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  line-height:18px;
}
#u3257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 30px;
  box-sizing:border-box;
  width:100%;
}
#u3257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3258_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:16px;
}
#u3258 {
  border-width:0px;
  position:absolute;
  left:916px;
  top:373px;
  width:15px;
  height:16px;
  display:flex;
}
#u3258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
