# 全局仪表盘演示文档

## 概述

本项目包含一个严格按照 MasterGo 设计的全局仪表盘页面，使用 Vue 3 + TypeScript + ECharts 开发。

## 功能特性

### 🎨 界面设计
- **顶部导航栏**：左侧logo和"rev-REITs"标题，右侧用户信息和通知图标
- **左侧菜单**：四个主要菜单项（全局仪表盘、行业情况展示、公募REITs产品及资产、市场动态）
- **响应式布局**：适配桌面端和移动端
- **现代化设计**：使用卡片式布局和阴影效果

### 📊 数据可视化
- **市场概览折线图**：展示公募REITs市场趋势
- **分行业市值占比饼图**：直观显示各行业占比
- **排行榜组件**：REITs涨幅TOP3展示
- **地域分布进度条**：资产地域分布可视化

### 📈 数据展示模块
- **REITs涨幅TOP3**：实时展示涨幅最高的REITs产品
- **分行业市值占比**：各行业市值分布情况
- **资产地域分布**：北京、上海、深圳等地区资产分布
- **行业日历**：重要事件和日期提醒
- **市场新闻**：最新市场动态和新闻
- **宏观数据**：GDP增长率、CPI、利率等关键指标
- **行业平均Cap Rate**：各行业资本化率对比

## 页面结构

```
全局仪表盘
├── 顶部导航栏
│   ├── 左侧：Logo + "rev-REITs"标题
│   └── 右侧：通知图标 + 用户信息下拉菜单
├── 左侧菜单
│   ├── 全局仪表盘（当前激活）
│   ├── 行业情况展示
│   ├── 公募REITs产品及资产
│   └── 市场动态
└── 主要内容区域
    ├── 公募REITs市场概览（折线图）
    ├── 数据卡片区域
    │   ├── REITs涨幅TOP3
    │   ├── 分行业市值占比（饼图）
    │   └── 资产地域分布
    └── 底部信息区域
        ├── 行业日历
        ├── 市场新闻
        ├── 宏观数据
        └── 行业平均Cap Rate
```

## 技术实现

### 前端技术栈
- **Vue 3**：使用 Composition API
- **TypeScript**：类型安全的开发体验
- **ECharts**：专业的数据可视化图表库
- **CSS Grid/Flexbox**：现代化的布局方案

### 图表功能
- **折线图**：展示市场趋势，支持平滑曲线和渐变填充
- **饼图**：展示行业占比，支持标签显示和颜色区分
- **进度条**：展示地域分布，支持动画效果

### 交互功能
- **菜单切换**：左侧菜单项点击切换
- **用户下拉菜单**：个人信息和退出登录
- **响应式适配**：不同屏幕尺寸的布局适配

## 使用说明

### 1. 访问仪表盘
1. 在登录页面输入用户名和密码
2. 勾选用户协议
3. 点击"登录"按钮
4. 登录成功后自动跳转到全局仪表盘页面

### 2. 查看数据
- **市场概览**：查看公募REITs市场的整体趋势
- **排行榜**：了解涨幅最高的REITs产品
- **行业占比**：分析各行业的市值分布
- **地域分布**：查看资产在不同地区的分布情况

### 3. 获取信息
- **行业日历**：查看重要事件和日期
- **市场新闻**：了解最新市场动态
- **宏观数据**：查看关键经济指标
- **Cap Rate**：了解各行业的资本化率

## 数据说明

### 示例数据
- **市场概览**：2021-2023年的市场表现数据
- **排行榜**：产业园区、仓储物流、基础设施等
- **行业占比**：产业园区35%、仓储物流25%、基础设施20%等
- **地域分布**：北京35%、上海28%、深圳22%、其他15%

### 数据更新
- 当前使用静态示例数据
- 可接入实际API数据源
- 支持实时数据更新

## 开发说明

### 项目结构
```
src/views/Dashboard.vue          # 全局仪表盘主组件
src/router/index.ts              # 路由配置
package.json                     # 项目依赖（包含ECharts）
```

### 关键依赖
- `echarts`: 数据可视化图表库
- `vue`: 前端框架
- `vue-router`: 路由管理

### 样式特点
- 使用CSS Grid实现响应式布局
- 卡片式设计，现代化UI
- 蓝色主题色（#1868F1）
- 阴影效果和圆角设计

## 扩展功能

### 可添加的功能
- **数据筛选**：按时间、行业、地区筛选
- **图表交互**：点击图表查看详情
- **数据导出**：导出图表和数据
- **实时更新**：接入WebSocket实时数据
- **个性化设置**：用户自定义布局

### 技术优化
- **性能优化**：图表懒加载和虚拟滚动
- **缓存策略**：数据缓存和图表缓存
- **错误处理**：完善的错误提示和重试机制
- **无障碍访问**：支持键盘导航和屏幕阅读器

## 部署说明

### 开发环境
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 访问地址
http://localhost:3000
```

### 生产环境
```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

## 总结

全局仪表盘页面严格按照 MasterGo 设计稿实现，提供了完整的REITs市场数据展示功能。页面布局清晰，数据可视化效果优秀，用户体验良好。通过Vue 3 + TypeScript + ECharts的技术栈，确保了代码的可维护性和扩展性。 