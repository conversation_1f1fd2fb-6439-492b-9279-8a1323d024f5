﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="240px" height="80px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="452px" y="256px" width="240px" height="80px" filterUnits="userSpaceOnUse" id="filter26">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget27">
      <path d="M 462 268  A 2 2 0 0 1 464 266 L 680 266  A 2 2 0 0 1 682 268 L 682 324  A 2 2 0 0 1 680 326 L 464 326  A 2 2 0 0 1 462 324 L 462 268  Z " fill-rule="nonzero" fill="#000000" stroke="none" fill-opacity="0.4980392156862745" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -452 -256 )">
    <use xlink:href="#widget27" filter="url(#filter26)" />
    <use xlink:href="#widget27" />
  </g>
</svg>