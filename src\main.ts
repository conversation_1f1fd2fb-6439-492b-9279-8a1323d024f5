import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 导入 Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// 导入 UnoCSS
import 'virtual:uno.css'

// 导入自定义样式
import './styles/index.css'

const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(Antd)

app.mount('#app') 