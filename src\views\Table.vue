<template>
  <div class="table-page">
    <a-page-header title="表格展示" subtitle="VXE-Table 集成示例">
      <template #extra>
        <a-button @click="$router.push('/')">返回首页</a-button>
      </template>
    </a-page-header>
    
    <div class="table-content">
      <a-card title="数据表格" class="table-card">
        <template #extra>
          <a-space>
            <a-button type="primary" @click="handleAdd">添加</a-button>
            <a-button @click="handleRefresh">刷新</a-button>
          </a-space>
        </template>
        
        <vxe-table
          ref="tableRef"
          :data="tableData"
          :loading="loading"
          stripe
          border
          show-overflow
          height="400"
          @cell-click="handleCellClick"
        >
          <vxe-column field="id" title="ID" width="80"></vxe-column>
          <vxe-column field="name" title="姓名" width="120"></vxe-column>
          <vxe-column field="email" title="邮箱" width="200"></vxe-column>
          <vxe-column field="role" title="角色" width="100">
            <template #default="{ row }">
              <a-tag :color="getRoleColor(row.role)">{{ row.role }}</a-tag>
            </template>
          </vxe-column>
          <vxe-column field="status" title="状态" width="100">
            <template #default="{ row }">
              <a-badge 
                :status="row.status === '活跃' ? 'success' : 'default'"
                :text="row.status"
              />
            </template>
          </vxe-column>
          <vxe-column field="createTime" title="创建时间" width="160"></vxe-column>
          <vxe-column title="操作" width="150" fixed="right">
            <template #default="{ row }">
              <a-space>
                <a-button size="small" @click="handleEdit(row)">编辑</a-button>
                <a-button size="small" danger @click="handleDelete(row)">删除</a-button>
              </a-space>
            </template>
          </vxe-column>
        </vxe-table>
        
        <div class="table-footer">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            show-size-changer
            show-quick-jumper
            :show-total="(total: number) => `共 ${total} 条记录`"
            @change="handlePageChange"
            @showSizeChange="handlePageSizeChange"
          />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { VxeTableInstance } from 'vxe-table'

interface TableRow {
  id: number
  name: string
  email: string
  role: string
  status: string
  createTime: string
}

const tableRef = ref<VxeTableInstance>()
const loading = ref(false)
const tableData = ref<TableRow[]>([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 模拟数据
const mockData: TableRow[] = [
  { id: 1, name: '张三', email: '<EMAIL>', role: '管理员', status: '活跃', createTime: '2024-01-01 10:00:00' },
  { id: 2, name: '李四', email: '<EMAIL>', role: '用户', status: '活跃', createTime: '2024-01-02 11:00:00' },
  { id: 3, name: '王五', email: '<EMAIL>', role: '编辑', status: '禁用', createTime: '2024-01-03 12:00:00' },
  { id: 4, name: '赵六', email: '<EMAIL>', role: '用户', status: '活跃', createTime: '2024-01-04 13:00:00' },
  { id: 5, name: '陈七', email: '<EMAIL>', role: '管理员', status: '活跃', createTime: '2024-01-05 14:00:00' }
]

onMounted(() => {
  loadData()
})

const loadData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    tableData.value = mockData
    pagination.total = mockData.length
    loading.value = false
  }, 500)
}

const getRoleColor = (role: string) => {
  const colorMap: Record<string, string> = {
    '管理员': 'red',
    '编辑': 'orange',
    '用户': 'blue'
  }
  return colorMap[role] || 'default'
}

const handleAdd = () => {
  message.info('添加功能待实现')
}

const handleEdit = (row: TableRow) => {
  message.info(`编辑用户: ${row.name}`)
}

const handleDelete = (row: TableRow) => {
  message.success(`删除用户: ${row.name}`)
}

const handleRefresh = () => {
  loadData()
  message.success('数据已刷新')
}

const handleCellClick = ({ row, column }: any) => {
  console.log('单元格点击:', row, column)
}

const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.pageSize = pageSize
  loadData()
}

const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1
  pagination.pageSize = size
  loadData()
}
</script>

<style scoped>
.table-page {
  padding: 24px;
}

.table-content {
  margin-top: 16px;
}

.table-card {
  min-height: 600px;
}

.table-footer {
  margin-top: 16px;
  text-align: right;
}
</style> 