﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.4980392156862745);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:954px;
  display:flex;
}
#u2 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#1868F1;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:38px;
  width:145px;
  height:25px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#1868F1;
}
#u4 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:30px;
  width:40px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u5 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:560px;
  height:551px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.8);
  border:none;
  border-radius:20px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:1172px;
  top:201px;
  width:560px;
  height:551px;
  display:flex;
}
#u6 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1868F1;
  text-align:center;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:1213px;
  top:241px;
  width:80px;
  height:21px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1868F1;
  text-align:center;
}
#u7 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u9_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:243px;
  width:144px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u9 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u9_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u10_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:1696px;
  top:246px;
  width:6px;
  height:10px;
  display:flex;
}
#u10 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u11_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:324px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u11 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:307px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u12 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:394px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:right;
}
#u13 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u14_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:377px;
  width:426px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u15_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:529px;
  width:500px;
  height:50px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u15_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u17 label {
  left:0px;
  width:100%;
}
#u17_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:507px;
  width:16px;
  height:12px;
  display:flex;
}
#u17 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u17_img.selected {
}
#u17.selected {
}
#u17_img.disabled {
}
#u17.disabled {
}
#u17_img.selected.error {
}
#u17.selected.error {
}
#u17_img.selected.hint {
}
#u17.selected.hint {
}
#u17_img.selected.error.hint {
}
#u17.selected.error.hint {
}
#u17_img.mouseOver.selected {
}
#u17.mouseOver.selected {
}
#u17_img.mouseOver.selected.error {
}
#u17.mouseOver.selected.error {
}
#u17_img.mouseOver.selected.hint {
}
#u17.mouseOver.selected.hint {
}
#u17_img.mouseOver.selected.error.hint {
}
#u17.mouseOver.selected.error.hint {
}
#u17_img.mouseDown.selected {
}
#u17.mouseDown.selected {
}
#u17_img.mouseDown.selected.error {
}
#u17.mouseDown.selected.error {
}
#u17_img.mouseDown.selected.hint {
}
#u17.mouseDown.selected.hint {
}
#u17_img.mouseDown.selected.error.hint {
}
#u17.mouseDown.selected.error.hint {
}
#u17_img.mouseOver.mouseDown.selected {
}
#u17.mouseOver.mouseDown.selected {
}
#u17_img.mouseOver.mouseDown.selected.error {
}
#u17.mouseOver.mouseDown.selected.error {
}
#u17_img.mouseOver.mouseDown.selected.hint {
}
#u17.mouseOver.mouseDown.selected.hint {
}
#u17_img.mouseOver.mouseDown.selected.error.hint {
}
#u17.mouseOver.mouseDown.selected.error.hint {
}
#u17_img.focused.selected {
}
#u17.focused.selected {
}
#u17_img.focused.selected.error {
}
#u17.focused.selected.error {
}
#u17_img.focused.selected.hint {
}
#u17.focused.selected.hint {
}
#u17_img.focused.selected.error.hint {
}
#u17.focused.selected.error.hint {
}
#u17_img.selected.disabled {
}
#u17.selected.disabled {
}
#u17_img.selected.hint.disabled {
}
#u17.selected.hint.disabled {
}
#u17_img.selected.error.disabled {
}
#u17.selected.error.disabled {
}
#u17_img.selected.error.hint.disabled {
}
#u17.selected.error.hint.disabled {
}
#u17_text {
  border-width:0px;
  position:absolute;
  left:12px;
  top:0px;
  width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u17_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:1218px;
  top:507px;
  width:252px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u18 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u19_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:1662px;
  top:392px;
  width:20px;
  height:20px;
  display:flex;
}
#u19 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:1333px;
  top:241px;
  width:80px;
  height:21px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u20 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:56px;
  height:11px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:1229px;
  top:272px;
  width:50px;
  height:5px;
  display:flex;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:1420px;
  top:599px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u22 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u22_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u23_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:1202px;
  top:662px;
  width:500px;
  height:1px;
  display:flex;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u24_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:1394px;
  top:655px;
  width:116px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u24 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 10px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u25_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:1371px;
  top:696px;
  width:163px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u25 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u26_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:99px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:891px;
  top:653px;
  width:241px;
  height:99px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u26 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u26_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
