﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="188px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="1526px" y="446px" width="188px" height="79px" filterUnits="userSpaceOnUse" id="filter32">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget33">
      <path d="M 1536 460  A 4 4 0 0 1 1540 456 L 1700 456  A 4 4 0 0 1 1704 460 L 1704 511  A 4 4 0 0 1 1700 515 L 1540 515  A 4 4 0 0 1 1536 511 L 1536 460  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -1526 -446 )">
    <use xlink:href="#widget33" filter="url(#filter32)" />
    <use xlink:href="#widget33" />
  </g>
</svg>