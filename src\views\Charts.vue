<template>
  <div class="charts">
    <a-page-header title="图表展示" subtitle="ECharts 集成示例">
      <template #extra>
        <a-button @click="$router.push('/')">返回首页</a-button>
      </template>
    </a-page-header>
    
    <div class="charts-content">
      <a-row :gutter="[16, 16]">
        <a-col :span="12">
          <a-card title="柱状图">
            <div ref="barChartRef" style="width: 100%; height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="饼图">
            <div ref="pieChartRef" style="width: 100%; height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="[16, 16]" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="折线图">
            <div ref="lineChartRef" style="width: 100%; height: 400px"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const barChartRef = ref<HTMLDivElement>()
const pieChartRef = ref<HTMLDivElement>()
const lineChartRef = ref<HTMLDivElement>()

onMounted(async () => {
  await nextTick()
  initBarChart()
  initPieChart()
  initLineChart()
})

const initBarChart = () => {
  if (!barChartRef.value) return
  
  const chart = echarts.init(barChartRef.value)
  const option = {
    title: {
      text: '月度销售数据'
    },
    tooltip: {},
    xAxis: {
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {},
    series: [{
      name: '销售额',
      type: 'bar',
      data: [5, 20, 36, 10, 10, 20],
      itemStyle: {
        color: '#1890ff'
      }
    }]
  }
  chart.setOption(option)
}

const initPieChart = () => {
  if (!pieChartRef.value) return
  
  const chart = echarts.init(pieChartRef.value)
  const option = {
    title: {
      text: '用户来源分布'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      name: '来源',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '搜索引擎' },
        { value: 735, name: '直接访问' },
        { value: 580, name: '邮件营销' },
        { value: 484, name: '联盟广告' },
        { value: 300, name: '视频广告' }
      ]
    }]
  }
  chart.setOption(option)
}

const initLineChart = () => {
  if (!lineChartRef.value) return
  
  const chart = echarts.init(lineChartRef.value)
  const option = {
    title: {
      text: '网站访问趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['访问量', '用户数']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '访问量',
        type: 'line',
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        smooth: true
      },
      {
        name: '用户数',
        type: 'line',
        data: [620, 732, 701, 734, 1090, 1130, 1120],
        smooth: true
      }
    ]
  }
  chart.setOption(option)
}
</script>

<style scoped>
.charts {
  padding: 24px;
}

.charts-content {
  margin-top: 16px;
}
</style> 