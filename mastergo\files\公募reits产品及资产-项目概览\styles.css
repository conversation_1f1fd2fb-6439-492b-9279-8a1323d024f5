﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1912px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1078_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1006px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1078 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:1006px;
  display:flex;
}
#u1078 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1078_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1080_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:884px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u1080 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:884px;
  display:flex;
}
#u1080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1081_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1081 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1081 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  -webkit-box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  box-shadow:5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
}
#u1082 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1912px;
  height:70px;
  display:flex;
}
#u1082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1083 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1084_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1084 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
}
#u1084 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1084_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u1085 {
  border-width:0px;
  position:absolute;
  left:1875px;
  top:33px;
  width:7px;
  height:4px;
  display:flex;
  color:#555555;
}
#u1085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1086 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1087_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1087 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:26px;
  width:73px;
  height:19px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1087 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1087_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1088 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:20px;
  width:30px;
  height:30px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u1088 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1088_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1089 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1090_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  -webkit-box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  box-shadow:0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
}
#u1090 {
  border-width:0px;
  position:absolute;
  left:1762px;
  top:59px;
  width:150px;
  height:153px;
  display:flex;
}
#u1090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1091_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u1091 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:79px;
  width:42px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
}
#u1091 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1091_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1092_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1092 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:134px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1092 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1092_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1093_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1093 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:178px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1868F1;
  text-align:center;
}
#u1093 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1093_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:2px;
}
#u1094 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:113px;
  width:130px;
  height:1px;
  display:flex;
}
#u1094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1095_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1095 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:122px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1096_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1096 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:174px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1097_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u1097 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:226px;
  width:200px;
  height:50px;
  display:flex;
  color:#555555;
}
#u1097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1098_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1098 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:88px;
  width:70px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1098 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1098_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1099_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1099 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:140px;
  width:84px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1099 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1099_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1100 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:192px;
  width:129px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1100 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1100_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:14px;
  background:inherit;
  background-color:rgba(0, 121, 254, 0);
  border:none;
  border-radius:5px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1101 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:244px;
  width:56px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1101 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1101_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1102 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:241px;
  width:20px;
  height:20px;
  display:flex;
}
#u1102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1103 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:85px;
  width:20px;
  height:20px;
  display:flex;
}
#u1103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1104 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:137px;
  width:20px;
  height:20px;
  display:flex;
}
#u1104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1105 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:189px;
  width:20px;
  height:20px;
  display:flex;
}
#u1105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1106_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1106 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:80px;
  width:1692px;
  height:60px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1107_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:3px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:5px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1107 {
  border-width:0px;
  position:absolute;
  left:272px;
  top:137px;
  width:60px;
  height:3px;
  display:flex;
}
#u1107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1108 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#1868F1;
  text-align:center;
}
#u1108 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1108_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1109 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1109 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1109_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1110 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:102px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1110 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1110_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1111_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1111 {
  border-width:0px;
  position:absolute;
  left:642px;
  top:102px;
  width:80px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:center;
}
#u1111 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1111_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:800px;
  height:388px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1112 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:150px;
  width:800px;
  height:388px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1113 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:165px;
  width:84px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1113 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1113_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1115_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:438px;
  width:733px;
  height:1px;
  display:flex;
}
#u1115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1116_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:372px;
  width:733px;
  height:1px;
  display:flex;
}
#u1116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1117_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:339px;
  width:733px;
  height:1px;
  display:flex;
}
#u1117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1118_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:306px;
  width:733px;
  height:1px;
  display:flex;
}
#u1118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1119_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1119 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:273px;
  width:733px;
  height:1px;
  display:flex;
}
#u1119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:429px;
  width:7px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1120 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1120_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:330px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1121 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1121_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:264px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1122 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1122_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1123_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:449px;
  width:48px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1124 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1124_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:363px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1125 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1125_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:297px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1126 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1126_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1127_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:405px;
  width:733px;
  height:1px;
  display:flex;
}
#u1127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:396px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1128 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1128_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1129_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:333px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1130 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1130_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1131_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:409px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1132 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1132_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1133_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1134 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1134_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:196px;
  width:24px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1135 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1135_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1136_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:586px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1137 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1137_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1138_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:662px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1138 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1139 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1139_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1140_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1140 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1140 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1141 {
  border-width:0px;
  position:absolute;
  left:710px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1141 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1141_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1142_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1142 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1143 {
  border-width:0px;
  position:absolute;
  left:785px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1143 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1143_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1144_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1144 {
  border-width:0px;
  position:absolute;
  left:888px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1144 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1145 {
  border-width:0px;
  position:absolute;
  left:861px;
  top:449px;
  width:55px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1145 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1145_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:6px;
}
#u1146 {
  border-width:0px;
  position:absolute;
  left:963px;
  top:439px;
  width:1px;
  height:5px;
  display:flex;
}
#u1146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:734px;
  height:2px;
}
#u1147 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:240px;
  width:733px;
  height:1px;
  display:flex;
}
#u1147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1148 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:231px;
  width:27px;
  height:20px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:right;
  line-height:20px;
}
#u1148 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1148_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1149 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1150 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:408px;
  width:21px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:16px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1151 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:392px;
  width:21px;
  height:16px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:11px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1152 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:381px;
  width:21px;
  height:11px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1153 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:376px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:8px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1154 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:368px;
  width:21px;
  height:8px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1155 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:366px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:9px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1156 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:357px;
  width:21px;
  height:9px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:6px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1157 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:350px;
  width:21px;
  height:6px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1158 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:347px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1158 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:26px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1159 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:321px;
  width:21px;
  height:26px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1160 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:26px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1161 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:412px;
  width:21px;
  height:26px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1162 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:408px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:7px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1163 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:400px;
  width:21px;
  height:7px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1164 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:397px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1165 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:392px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1166 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:390px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:6px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1167 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:384px;
  width:21px;
  height:6px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1168 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:380px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1169 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:378px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:22px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1170 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:356px;
  width:21px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1171 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:35px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1172 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:403px;
  width:21px;
  height:35px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1173 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:398px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:10px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1174 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:388px;
  width:21px;
  height:10px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1175 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:384px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:7px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1176 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:377px;
  width:21px;
  height:7px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1177 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:375px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:8px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1178 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:366px;
  width:21px;
  height:8px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1178 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1179_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:6px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1179 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:361px;
  width:21px;
  height:6px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1180 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:358px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:30px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1181 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:329px;
  width:21px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1182 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:34px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1183 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:404px;
  width:21px;
  height:34px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1184 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:399px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:9px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1185 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:390px;
  width:21px;
  height:9px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1186 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:386px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:7px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1187 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:379px;
  width:21px;
  height:7px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1188_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1188 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:377px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:8px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1189 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:369px;
  width:21px;
  height:8px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1190 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:364px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1191 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:361px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:28px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1192 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:333px;
  width:21px;
  height:28px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1193 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:20px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1194 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:418px;
  width:21px;
  height:20px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1195 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:415px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1196 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:409px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1197 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:407px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1198_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1198 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:403px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:1px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1199 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:402px;
  width:21px;
  height:1px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1200_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1200 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:397px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1201 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:394px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1202_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1202 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:392px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:17px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1203 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:376px;
  width:21px;
  height:17px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1204 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:17px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1205 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:421px;
  width:21px;
  height:17px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1206 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:419px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1207 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:414px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1208 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:412px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1209 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:409px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:1px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1210 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:408px;
  width:21px;
  height:1px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1211 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:404px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1212 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:401px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:1px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1213 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:400px;
  width:21px;
  height:1px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:14px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1214 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:386px;
  width:21px;
  height:14px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1215 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:18px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1216 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:420px;
  width:21px;
  height:18px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1217_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1217 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:417px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1218 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:412px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1219 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:410px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1219_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1220 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:406px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1220 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1221_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:1px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1221 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:405px;
  width:21px;
  height:1px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1222 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:400px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1223 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:398px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:1px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1224 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:396px;
  width:21px;
  height:1px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:16px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1225 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:381px;
  width:21px;
  height:16px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:24px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1227 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:414px;
  width:21px;
  height:24px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1228 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:410px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:7px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1229 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:403px;
  width:21px;
  height:7px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1229 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:3px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1230 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:400px;
  width:21px;
  height:3px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1231 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:396px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1232 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:394px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:6px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1233 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:388px;
  width:21px;
  height:6px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1234 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:385px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1235 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:383px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:20px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1236 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:362px;
  width:21px;
  height:20px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1237 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:29px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1238 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:409px;
  width:21px;
  height:29px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1238 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1239_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1239 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:404px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:8px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1240 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:396px;
  width:21px;
  height:8px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1240 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1241_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1241 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:393px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1241 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:6px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1242 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:387px;
  width:21px;
  height:6px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1243 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:385px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:7px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1244 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:378px;
  width:21px;
  height:7px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1245 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:374px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:2px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1246 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:371px;
  width:21px;
  height:2px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:25px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1247 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:347px;
  width:21px;
  height:25px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1248 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:60px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1249 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:378px;
  width:21px;
  height:60px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1249 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:9px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1250 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:369px;
  width:21px;
  height:9px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:17px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1251 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:353px;
  width:21px;
  height:17px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1251 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1252_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:8px;
  background:inherit;
  background-color:rgba(236, 128, 141, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1252 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:345px;
  width:21px;
  height:8px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:12px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1253 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:333px;
  width:21px;
  height:12px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:4px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1254 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:330px;
  width:21px;
  height:4px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:14px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1255 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:315px;
  width:21px;
  height:14px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:10px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1256 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:306px;
  width:21px;
  height:10px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:5px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1257 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:301px;
  width:21px;
  height:5px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:51px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  border:none;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1258 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:251px;
  width:21px;
  height:51px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1259 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1260 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1261 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1262_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:165px;
  height:79px;
}
#u1262 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:340px;
  width:145px;
  height:59px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1262 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1263_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:22px;
  background:inherit;
  background-color:rgba(8, 12, 64, 0.047058823529411764);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1263 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:367px;
  width:125px;
  height:22px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1264 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:350px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u1264 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1265_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u1265 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:375px;
  width:6px;
  height:6px;
  display:flex;
}
#u1265 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1266 {
  border-width:0px;
  position:absolute;
  left:473px;
  top:372px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  line-height:12px;
}
#u1266 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1266_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u1267 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:372px;
  width:39px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#1868F1;
  text-align:right;
  line-height:12px;
}
#u1267 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1267_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1268 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:449px;
  width:62px;
  height:18px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(170, 170, 170, 0.9921568627450981);
  text-align:center;
  line-height:18px;
}
#u1268 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1268_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1269_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u1269 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:163px;
  width:2px;
  height:20px;
  display:flex;
}
#u1269 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1270 {
  border-width:0px;
  position:absolute;
  left:349px;
  top:488px;
  width:10px;
  height:10px;
  display:flex;
}
#u1270 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1270_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1271 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:487px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1271 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1271_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(194, 128, 255, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1272 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:488px;
  width:10px;
  height:10px;
  display:flex;
}
#u1272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1273 {
  border-width:0px;
  position:absolute;
  left:780px;
  top:487px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1273 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1273_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(47, 194, 91, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1274 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:488px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1274 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1274_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1275 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:487px;
  width:84px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1275 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1275_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(245, 154, 35, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1276 {
  border-width:0px;
  position:absolute;
  left:551px;
  top:488px;
  width:10px;
  height:10px;
  display:flex;
}
#u1276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1276_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1277 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:487px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1277 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1277_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(236, 128, 141, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1278 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:488px;
  width:10px;
  height:10px;
  display:flex;
}
#u1278 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1279 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:487px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1279 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1279_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(128, 128, 255, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#8400FF;
}
#u1280 {
  border-width:0px;
  position:absolute;
  left:848px;
  top:489px;
  width:10px;
  height:10px;
  display:flex;
  color:#8400FF;
}
#u1280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1281_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1281 {
  border-width:0px;
  position:absolute;
  left:863px;
  top:488px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1281 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1281_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(19, 190, 180, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1282 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:510px;
  width:10px;
  height:10px;
  display:flex;
}
#u1282 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1283_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1283 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:509px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1283 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1283_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1284_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(129, 211, 248, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1284 {
  border-width:0px;
  position:absolute;
  left:551px;
  top:510px;
  width:10px;
  height:10px;
  display:flex;
}
#u1284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1284_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1285 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:509px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1285 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1285_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(128, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1286 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:510px;
  width:10px;
  height:10px;
  display:flex;
}
#u1286 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1287_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1287 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:509px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1287 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1287_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
  background:inherit;
  background-color:rgba(202, 249, 130, 0.9921568627450981);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1288 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:510px;
  width:10px;
  height:10px;
  display:flex;
}
#u1288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1289 {
  border-width:0px;
  position:absolute;
  left:780px;
  top:509px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1289 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1289_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:882px;
  height:388px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1290 {
  border-width:0px;
  position:absolute;
  left:1020px;
  top:150px;
  width:882px;
  height:388px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1291 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:165px;
  width:32px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1291 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1291_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1292_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u1292 {
  border-width:0px;
  position:absolute;
  left:1020px;
  top:163px;
  width:2px;
  height:20px;
  display:flex;
}
#u1292 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1293 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:210px;
  width:852px;
  height:328px;
}
#u1293_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:328px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1293_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1294 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1295 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1296 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1296 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1297 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1297 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1298 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:180px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1298 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1299 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1299 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1300 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:300px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1300 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1300_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:852px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1301 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:360px;
  width:852px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1301 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1302 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1303 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:9px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1303 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1303_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1304 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:39px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1304 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1304_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1305 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:69px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1305 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1305_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1306 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:99px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1306 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1306_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1307 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:129px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1307 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1307_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1308 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:159px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1308 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1308_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1309 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:189px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1309 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1309_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1310 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:219px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1310 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1310_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1311 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:249px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1311 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1311_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1312 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:279px;
  width:96px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1312 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1313_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1313 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:309px;
  width:96px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1313 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1313_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1314 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:339px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1314 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1314_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1315_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1315 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:369px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1315 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1315_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1316 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1317 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:9px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1317 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1317_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1318 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:39px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1318 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1318_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1319 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:69px;
  width:70px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1319 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1319_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1320 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:99px;
  width:70px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1320 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1320_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1321 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:129px;
  width:154px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1321 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1321_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1322 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:159px;
  width:82px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1322 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1322_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1323_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1323 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:189px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1323 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1323_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1324_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1324 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:219px;
  width:130px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1324 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1324_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1325_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1325 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:249px;
  width:142px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1325 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1325_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1326 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:279px;
  width:154px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1326 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1326_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1327 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:309px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1327 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1327_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1328_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1328 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:339px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1328 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1328_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1329 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:369px;
  width:94px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1329 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1329_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1330 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1331_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1331 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:9px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1331 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1331_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1332 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:39px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1332 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1332_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1333 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:69px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1333 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1333_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1334 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:99px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1334 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1334_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1335 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:129px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1335 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1335_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1336 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:159px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1336 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1336_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1337_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1337 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:189px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1337 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1337_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1338 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:219px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1338 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1338_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1339_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1339 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:249px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1339 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1339_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1340 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:279px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1340 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1340_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1341_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1341 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:309px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1341 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1341_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1342 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:339px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1342 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1342_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1343_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1343 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:369px;
  width:166px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1343 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1343_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1344 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1345 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1346 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1347_img {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-10px;
  width:240px;
  height:80px;
}
#u1347 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:266px;
  width:220px;
  height:60px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1347 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1347_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u1348 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:276px;
  width:200px;
  height:40px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u1348 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1348_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1349 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1350_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1350 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:9px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1350 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1350_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1351_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1351 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:39px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1351 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1351_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1352 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:69px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1352 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1352_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1353_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1353 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:99px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1353 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1353_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1354 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:129px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1354 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1354_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1355 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:159px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1355 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1355_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1356 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:189px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1356 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1356_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1357 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:219px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1357 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1357_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1358 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:249px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1358 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1358_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1359_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1359 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:279px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1359 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1359_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1360_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1360 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:309px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1360 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1360_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1361 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:339px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1361 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1361_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1362 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:369px;
  width:62px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1362 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1362_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1363 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1364_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1364 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:9px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1364 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1364_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1365_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1365 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:39px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1365 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1365_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1366_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1366 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:69px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1366 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1366_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1367 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:99px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1367 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1367_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1368_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1368 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:129px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1368 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1368_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1369 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:159px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1369 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1369_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1370_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1370 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:189px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1370 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1370_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1371 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:219px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1371 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1371_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1372_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1372 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:249px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1372 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1372_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1373_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1373 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:279px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1373 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1373_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1374_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1374 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:309px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1374 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1374_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1375_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1375 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:339px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1375 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1375_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1376_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1376 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:369px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1376 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1376_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1377 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1378_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1378 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:9px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1378 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1378_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1379_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1379 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:39px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1379 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1379_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1380_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1380 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:69px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1380 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1380_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1381 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:99px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1381 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1381_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1382 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:129px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1382 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1382_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1383 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:159px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1383 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1383_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1384_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1384 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:189px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1384 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1384_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1385_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1385 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:219px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1385 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1385_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1386_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1386 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:249px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1386 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1386_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1387_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1387 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:279px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1387 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1387_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1388_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1388 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:309px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1388 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1388_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1389_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1389 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:339px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1389 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1389_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1390_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1390 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:369px;
  width:190px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1390 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1390_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1391_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1692px;
  height:448px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1391 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:548px;
  width:1692px;
  height:448px;
  display:flex;
  opacity:0.9;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1391 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1391_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1392_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1392 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:563px;
  width:64px;
  height:16px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1392 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1392_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1393_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u1393 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:561px;
  width:2px;
  height:20px;
  display:flex;
}
#u1393 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1393_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1394_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
  text-align:left;
}
#u1394 {
  border-width:0px;
  position:absolute;
  left:309px;
  top:559px;
  width:108px;
  height:24px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
  text-align:left;
}
#u1394 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1394_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1395_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:4px;
}
#u1395 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:569px;
  width:7px;
  height:4px;
  display:flex;
}
#u1395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:59px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u1396 {
  border-width:0px;
  position:absolute;
  left:1713px;
  top:317px;
  width:137px;
  height:59px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:20px;
}
#u1396 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1397 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1662px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1398 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:608px;
  width:1662px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1662px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1399 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:668px;
  width:1662px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1399 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1662px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1400 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:728px;
  width:1662px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1401_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1662px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1401 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:788px;
  width:1662px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1401 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1662px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1402 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:848px;
  width:1662px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1402 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1403_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1662px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 104, 241, 0.09803921568627451);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1403 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:908px;
  width:1662px;
  height:30px;
  display:flex;
  color:rgba(255, 255, 255, 0.7490196078431373);
}
#u1403 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1404 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1405 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1405 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1405_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1406 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:647px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1406 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1406_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1407 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:677px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1407 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1407_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1408 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:707px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1408 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1408_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1409_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1409 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:737px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1409 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1409_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1410 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:767px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1410 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1410_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1411 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:797px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1411 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1411_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1412 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:827px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1412 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1412_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1413 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:857px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1413 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1413_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1414 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:887px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1414 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1414_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1415 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:917px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1415 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1415_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1416 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1417 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1417 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1417_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1418 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:647px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1418 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1418_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1419 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:677px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1419 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1419_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1420 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:707px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1420 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1420_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1421 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:737px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1421 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1421_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1422 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:767px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1422 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1422_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1423 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:797px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1423 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1423_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1424 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:827px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1424 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1424_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1425_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1425 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:857px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1425 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1425_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1426 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:887px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1426 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1426_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1427 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:917px;
  width:55px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1427 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1427_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1428 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1429 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:617px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1429 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1429_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1430 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:647px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1430 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1430_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1431 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:677px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1431 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1431_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1432_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1432 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:707px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1432 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1432_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1433_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1433 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:737px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1433 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1433_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1434 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:767px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1434 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1434_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1435 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:797px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1435 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1435_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1436 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:827px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1436 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1436_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1437 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:857px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1437 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1437_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1438 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:887px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1438 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1438_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1439 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:917px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1439 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1439_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1440 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1441 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1442 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1443 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1444 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1444 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1444_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1445 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:647px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1445 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1445_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1446 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:677px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1446 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1446_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1447 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:707px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1447 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1447_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1448 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:737px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1448 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1448_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1449 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:767px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1449 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1449_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1450 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:797px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1450 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1450_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1451_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1451 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:827px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1451 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1451_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1452 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:857px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1452 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1452_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1453_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1453 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:887px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1453 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1453_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1454 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:917px;
  width:108px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1454 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1454_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1455 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1456 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1456 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1456_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1457 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:647px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1457 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1457_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1458 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:677px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1458 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1458_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1459 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:707px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1459 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1459_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1460 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:737px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1460 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1460_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1461 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:767px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1461 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1461_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1462 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:797px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1462 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1462_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1463 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:827px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1463 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1463_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1464 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:857px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1464 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1464_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1465 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:887px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1465 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1465_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1466 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:917px;
  width:168px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1466 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1466_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1467 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1468_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1468 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:617px;
  width:72px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1468 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1468_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1469_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1469 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:647px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1469 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1469_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1470 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:677px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1470 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1470_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1471 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:707px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1471 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1471_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1472 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:737px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1472 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1472_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1473 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:767px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1473 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1473_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1474 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:797px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1474 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1474_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1475_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1475 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:827px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1475 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1475_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1476 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:857px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1476 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1476_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1477 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:887px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1477 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1477_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1478 {
  border-width:0px;
  position:absolute;
  left:1285px;
  top:917px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1478 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1478_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1479 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1480 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1480 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1480_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1481_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1481 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:647px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1481 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1481_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1482_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1482 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:677px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1482 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1482_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1483_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1483 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:707px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1483 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1483_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1484_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1484 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:737px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1484 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1484_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1485 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:767px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1485 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1485_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1486_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1486 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:797px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1486 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1486_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1487_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1487 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:827px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1487 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1487_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1488_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1488 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:857px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1488 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1488_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1489_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1489 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:887px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1489 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1489_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1490_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1490 {
  border-width:0px;
  position:absolute;
  left:1451px;
  top:917px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1490 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1490_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1491 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1492 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1493 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1494 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1495_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1495 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1495 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1495_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1496_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1496 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:647px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1496 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1496_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1497 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:677px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1497 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1497_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1498_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1498 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:707px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1498 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1498_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1499_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1499 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:737px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1499 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1499_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1500_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1500 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:767px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1500 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1500_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1501 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:797px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1501 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1501_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1502_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1502 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:827px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1502 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1502_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1503 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:857px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1503 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1503_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1504_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1504 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:887px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1504 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1504_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1505_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1505 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:917px;
  width:36px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1505 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1505_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1506 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1507 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1508 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1509 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1510_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1510 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:617px;
  width:48px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1510 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1510_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1511_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1511 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:647px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1511 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1511_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1512_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1512 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:677px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1512 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1512_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:707px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1513 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1513_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1514_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:737px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1514 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1514_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:767px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1515 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1515_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:797px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1516 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1516_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:827px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1517 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1517_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1518_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1518 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:857px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1518 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1518_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1519_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1519 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:887px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1519 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1519_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1520_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:917px;
  width:132px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1520 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1520_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1525 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:617px;
  width:24px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1528 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1528_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1529_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:647px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1529 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1529_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:677px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1530 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1530_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:707px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1531 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1531_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1532 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:737px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1532 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1532_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1533 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:767px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1533 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1533_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1534 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:797px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1534 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1534_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1535_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1535 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:827px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1535 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1535_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1536 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:857px;
  width:7px;
  height:12px;
  display:flex;
  opacity:0.8;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1536 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1536_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1537 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:887px;
  width:7px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1537 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1537_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1538 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:917px;
  width:14px;
  height:12px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#7F7F7F;
  line-height:12px;
}
#u1538 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1538_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1539 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(24, 104, 241, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FFFFFF;
  text-align:center;
  line-height:20px;
}
#u1540 {
  border-width:0px;
  position:absolute;
  left:1578px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FFFFFF;
  text-align:center;
  line-height:20px;
}
#u1540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1541 {
  border-width:0px;
  position:absolute;
  left:1644px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1541 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1541_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1542 {
  border-width:0px;
  position:absolute;
  left:1677px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1542 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1543_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1543 {
  border-width:0px;
  position:absolute;
  left:1611px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1543 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1544_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:center;
  line-height:20px;
}
#u1544 {
  border-width:0px;
  position:absolute;
  left:1545px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:center;
  line-height:20px;
}
#u1544 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1545_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:center;
  line-height:20px;
}
#u1545 {
  border-width:0px;
  position:absolute;
  left:1743px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:center;
  line-height:20px;
}
#u1545 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1546_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1546 {
  border-width:0px;
  position:absolute;
  left:1710px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1546 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1547 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u1548 {
  border-width:0px;
  position:absolute;
  left:1843px;
  top:953px;
  width:28px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u1548 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1548_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(20, 92, 199, 1);
  border-radius:4px;
  -moz-box-shadow:0px 0px 6px rgba(20, 92, 199, 0.4980392156862745);
  -webkit-box-shadow:0px 0px 6px rgba(20, 92, 199, 0.4980392156862745);
  box-shadow:0px 0px 6px rgba(20, 92, 199, 0.4980392156862745);
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u1548.mouseOver {
}
#u1548_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
  background:inherit;
  background-color:rgba(229, 229, 229, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
}
#u1548.disabled {
}
#u1548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1549_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1549 {
  border-width:0px;
  position:absolute;
  left:1816px;
  top:962px;
  width:22px;
  height:11px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1549 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1549_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1550_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1550 {
  border-width:0px;
  position:absolute;
  left:1876px;
  top:962px;
  width:11px;
  height:11px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1550 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1550_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1551_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1551 {
  border-width:0px;
  position:absolute;
  left:1510px;
  top:953px;
  width:30px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1551 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1552_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1552 {
  border-width:0px;
  position:absolute;
  left:1776px;
  top:953px;
  width:30px;
  height:28px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:center;
  line-height:20px;
}
#u1552 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1552_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1553_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:right;
}
#u1553 {
  border-width:0px;
  position:absolute;
  left:1453px;
  top:962px;
  width:47px;
  height:11px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  text-align:right;
}
#u1553 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1553_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1554 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1555_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1555 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:160px;
  width:198px;
  height:40px;
  display:flex;
}
#u1555 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1556_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1556 {
  border-width:0px;
  position:absolute;
  left:827px;
  top:173px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1556 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1556_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1557_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1557 {
  border-width:0px;
  position:absolute;
  left:949px;
  top:165px;
  width:30px;
  height:30px;
  display:flex;
}
#u1557 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1557_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1558_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1558 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:170px;
  width:18px;
  height:20px;
  display:flex;
}
#u1558 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1558_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1559_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1559 {
  border-width:0px;
  position:absolute;
  left:989px;
  top:175px;
  width:6px;
  height:10px;
  display:flex;
}
#u1559 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1560 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1561_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1561 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:160px;
  width:198px;
  height:40px;
  display:flex;
}
#u1561 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1562 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:173px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1562 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1562_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1563_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1563 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:165px;
  width:30px;
  height:30px;
  display:flex;
}
#u1563 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1563_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1564_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1564 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:170px;
  width:18px;
  height:20px;
  display:flex;
}
#u1564 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1564_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1565_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1565 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:175px;
  width:6px;
  height:10px;
  display:flex;
}
#u1565 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1566 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1567_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:40px;
}
#u1567 {
  border-width:0px;
  position:absolute;
  left:1704px;
  top:558px;
  width:198px;
  height:40px;
  display:flex;
}
#u1567 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1568 {
  border-width:0px;
  position:absolute;
  left:1719px;
  top:571px;
  width:112px;
  height:14px;
  display:flex;
  font-family:"STHeiti SC Medium", "STHeiti SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:right;
}
#u1568 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1568_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1569_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1569 {
  border-width:0px;
  position:absolute;
  left:1841px;
  top:563px;
  width:30px;
  height:30px;
  display:flex;
}
#u1569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1570_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:20px;
}
#u1570 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:568px;
  width:18px;
  height:20px;
  display:flex;
}
#u1570 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1571_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:10px;
}
#u1571 {
  border-width:0px;
  position:absolute;
  left:1881px;
  top:573px;
  width:6px;
  height:10px;
  display:flex;
}
#u1571 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
