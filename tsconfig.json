{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "strict": true, "noEmit": true, "useDefineForClassFields": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}