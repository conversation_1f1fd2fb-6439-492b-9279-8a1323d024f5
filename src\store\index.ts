import { defineStore } from 'pinia'

export const useMainStore = defineStore('main', {
  state: () => ({
    count: 0,
    user: null as any,
    loading: false
  }),
  
  getters: {
    doubleCount: (state) => state.count * 2
  },
  
  actions: {
    increment() {
      this.count++
    },
    
    setUser(user: any) {
      this.user = user
    },
    
    setLoading(status: boolean) {
      this.loading = status
    }
  }
}) 