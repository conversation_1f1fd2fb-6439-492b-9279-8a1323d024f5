﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="153px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="452px" y="318px" width="153px" height="79px" filterUnits="userSpaceOnUse" id="filter34">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget35">
      <path d="M 462 332  A 4 4 0 0 1 466 328 L 591 328  A 4 4 0 0 1 595 332 L 595 383  A 4 4 0 0 1 591 387 L 466 387  A 4 4 0 0 1 462 383 L 462 332  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -452 -318 )">
    <use xlink:href="#widget35" filter="url(#filter34)" />
    <use xlink:href="#widget35" />
  </g>
</svg>