<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon"></div>
          <div class="logo-text">rev-REITs</div>
        </div>
      </div>
      <div class="header-right">
        <div class="notification-icon">
          <i class="icon-bell"></i>
        </div>
        <div class="user-info">
          <span class="username">用户名</span>
          <div class="user-dropdown">
            <div class="dropdown-item">个人中心</div>
            <div class="dropdown-item">退出登录</div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单 -->
      <aside class="sidebar">
        <nav class="menu">
          <div class="menu-item active">
            <span>全局仪表盘</span>
          </div>
          <div class="menu-item">
            <span>行业情况展示</span>
          </div>
          <div class="menu-item">
            <span>公募REITs产品及资产</span>
          </div>
          <div class="menu-item">
            <span>市场动态</span>
          </div>
        </nav>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="content">
        <!-- 公募REITs市场概览 -->
        <section class="market-overview">
          <h2 class="section-title">公募REITs市场概览</h2>
          <div class="chart-container">
            <div class="chart-wrapper">
              <div class="chart" ref="marketChart"></div>
            </div>
          </div>
        </section>

        <!-- 数据卡片区域 -->
        <div class="data-cards">
          <!-- REITs涨幅TOP3 -->
          <div class="card ranking-card">
            <h3 class="card-title">REITs涨幅TOP3</h3>
            <div class="ranking-list">
              <div class="ranking-item">
                <span class="rank">1</span>
                <span class="name">产业园区</span>
                <span class="value">25.10%</span>
              </div>
              <div class="ranking-item">
                <span class="rank">2</span>
                <span class="name">仓储物流</span>
                <span class="value">18.50%</span>
              </div>
              <div class="ranking-item">
                <span class="rank">3</span>
                <span class="name">基础设施</span>
                <span class="value">12.30%</span>
              </div>
            </div>
          </div>

          <!-- 分行业市值占比 -->
          <div class="card pie-chart-card">
            <h3 class="card-title">分行业市值占比</h3>
            <div class="chart" ref="pieChart"></div>
          </div>

          <!-- 资产地域分布 -->
          <div class="card region-card">
            <h3 class="card-title">资产地域分布</h3>
            <div class="region-list">
              <div class="region-item">
                <span class="region">北京</span>
                <div class="progress-bar">
                  <div class="progress" style="width: 35%"></div>
                </div>
                <span class="percentage">35%</span>
              </div>
              <div class="region-item">
                <span class="region">上海</span>
                <div class="progress-bar">
                  <div class="progress" style="width: 28%"></div>
                </div>
                <span class="percentage">28%</span>
              </div>
              <div class="region-item">
                <span class="region">深圳</span>
                <div class="progress-bar">
                  <div class="progress" style="width: 22%"></div>
                </div>
                <span class="percentage">22%</span>
              </div>
              <div class="region-item">
                <span class="region">其他</span>
                <div class="progress-bar">
                  <div class="progress" style="width: 15%"></div>
                </div>
                <span class="percentage">15%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部信息区域 -->
        <div class="bottom-info">
          <!-- 行业日历 -->
          <div class="card calendar-card">
            <h3 class="card-title">行业日历</h3>
            <div class="calendar-content">
              <div class="calendar-item">
                <div class="date">2024-01-15</div>
                <div class="event">REITs产品发行</div>
              </div>
              <div class="calendar-item">
                <div class="date">2024-01-20</div>
                <div class="event">行业政策发布</div>
              </div>
              <div class="calendar-item">
                <div class="date">2024-01-25</div>
                <div class="event">市场分析报告</div>
              </div>
            </div>
          </div>

          <!-- 市场新闻 -->
          <div class="card news-card">
            <h3 class="card-title">市场新闻</h3>
            <div class="news-list">
              <div class="news-item">
                <div class="news-title">公募REITs市场持续回暖</div>
                <div class="news-time">2024-01-15 10:30</div>
              </div>
              <div class="news-item">
                <div class="news-title">新政策支持REITs发展</div>
                <div class="news-time">2024-01-14 15:20</div>
              </div>
              <div class="news-item">
                <div class="news-title">基础设施REITs表现亮眼</div>
                <div class="news-time">2024-01-13 09:15</div>
              </div>
            </div>
          </div>

          <!-- 宏观数据 -->
          <div class="card macro-card">
            <h3 class="card-title">宏观数据</h3>
            <div class="macro-data">
              <div class="data-item">
                <span class="label">GDP增长率</span>
                <span class="value">5.2%</span>
              </div>
              <div class="data-item">
                <span class="label">CPI</span>
                <span class="value">2.1%</span>
              </div>
              <div class="data-item">
                <span class="label">利率</span>
                <span class="value">3.85%</span>
              </div>
            </div>
          </div>

          <!-- 行业平均Cap Rate -->
          <div class="card cap-rate-card">
            <h3 class="card-title">行业平均Cap Rate</h3>
            <div class="cap-rate-data">
              <div class="rate-item">
                <span class="sector">产业园区</span>
                <span class="rate">4.8%</span>
              </div>
              <div class="rate-item">
                <span class="sector">仓储物流</span>
                <span class="rate">5.2%</span>
              </div>
              <div class="rate-item">
                <span class="sector">基础设施</span>
                <span class="rate">4.5%</span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const marketChart = ref<HTMLElement>()
const pieChart = ref<HTMLElement>()

onMounted(() => {
  // 初始化市场概览图表
  if (marketChart.value) {
    const chart = echarts.init(marketChart.value)
    const option = {
      grid: {
        left: '10%',
        right: '10%',
        top: '10%',
        bottom: '15%'
      },
      xAxis: {
        type: 'category',
        data: ['2021-06', '2021-12', '2022-06', '2022-12', '2023-06', '2023-12'],
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      series: [{
        data: [15, 25, 10, 35, 20, 30],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#1868F1',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(24, 104, 241, 0.3)'
            }, {
              offset: 1, color: 'rgba(24, 104, 241, 0.1)'
            }]
          }
        }
      }]
    }
    chart.setOption(option)
  }

  // 初始化饼图
  if (pieChart.value) {
    const chart = echarts.init(pieChart.value)
    const option = {
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 35, name: '产业园区', itemStyle: { color: '#1868F1' } },
          { value: 25, name: '仓储物流', itemStyle: { color: '#52C41A' } },
          { value: 20, name: '基础设施', itemStyle: { color: '#FAAD14' } },
          { value: 15, name: '商业地产', itemStyle: { color: '#F5222D' } },
          { value: 5, name: '其他', itemStyle: { color: '#722ED1' } }
        ],
        label: {
          show: true,
          formatter: '{b}: {d}%'
        }
      }]
    }
    chart.setOption(option)
  }
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 30px;
  height: 30px;
  background: #1868F1;
  border-radius: 50%;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1868F1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon-bell::before {
  content: '🔔';
  font-size: 20px;
}

.user-info {
  position: relative;
  cursor: pointer;
}

.username {
  font-size: 14px;
  color: #333;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: none;
  min-width: 120px;
}

.user-info:hover .user-dropdown {
  display: block;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.dropdown-item:hover {
  background: #f5f5f5;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  margin-top: 60px;
  min-height: calc(100vh - 60px);
}

/* 左侧菜单 */
.sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  padding-top: 20px;
}

.menu {
  display: flex;
  flex-direction: column;
}

.menu-item {
  padding: 15px 20px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.3s;
}

.menu-item:hover {
  background: #f5f5f5;
  color: #1868F1;
}

.menu-item.active {
  background: #f0f7ff;
  color: #1868F1;
  border-left-color: #1868F1;
}

/* 右侧内容区域 */
.content {
  flex: 1;
  padding: 20px;
  background: #f5f5f5;
}

/* 市场概览 */
.market-overview {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.chart-wrapper {
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 数据卡片 */
.data-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

/* 排行榜 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.rank {
  width: 24px;
  height: 24px;
  background: #1868F1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.value {
  font-size: 14px;
  font-weight: 600;
  color: #1868F1;
}

/* 饼图卡片 */
.pie-chart-card .chart {
  height: 200px;
}

/* 地域分布 */
.region-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.region-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.region {
  width: 60px;
  font-size: 14px;
  color: #333;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #1868F1;
  border-radius: 4px;
  transition: width 0.3s;
}

.percentage {
  width: 40px;
  font-size: 14px;
  color: #666;
  text-align: right;
}

/* 底部信息区域 */
.bottom-info {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* 日历卡片 */
.calendar-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.calendar-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.date {
  font-size: 12px;
  color: #666;
}

.event {
  font-size: 14px;
  color: #333;
}

/* 新闻卡片 */
.news-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.news-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.news-time {
  font-size: 12px;
  color: #999;
}

/* 宏观数据 */
.macro-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  font-weight: 600;
  color: #1868F1;
}

/* Cap Rate */
.cap-rate-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.sector {
  font-size: 14px;
  color: #333;
}

.rate {
  font-size: 14px;
  font-weight: 600;
  color: #1868F1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .bottom-info {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 160px;
  }
  
  .data-cards {
    grid-template-columns: 1fr;
  }
  
  .bottom-info {
    grid-template-columns: 1fr;
  }
}
</style> 