<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon"></div>
          <div class="logo-text">rev-REITs</div>
        </div>
      </div>
      <div class="header-right">
        <div class="notification-icon">
          <i class="icon-bell"></i>
        </div>
        <div class="user-info">
          <span class="username">用户名</span>
          <div class="user-dropdown">
            <div class="dropdown-item">个人中心</div>
            <div class="dropdown-item">退出登录</div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单 -->
      <aside class="sidebar">
        <nav class="menu">
          <div class="menu-item active">
            <span>全局仪表盘</span>
          </div>
          <div class="menu-item">
            <span>行业情况展示</span>
          </div>
          <div class="menu-item">
            <span>公募REITs产品及资产</span>
          </div>
          <div class="menu-item">
            <span>市场动态</span>
          </div>
        </nav>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="content">
        <div class="content-layout">
          <!-- 左侧图表区域 -->
          <div class="charts-area">
            <!-- 公募REITs市场概览 -->
            <section class="market-overview">
              <h2 class="section-title">公募REITs市场概览</h2>
              <div class="chart-container">
                <div class="chart-wrapper">
                  <div class="chart" ref="marketChart"></div>
                </div>
              </div>
            </section>

            <!-- 数据卡片区域 -->
            <div class="data-cards">
              <!-- 分行业市值占比 -->
              <div class="card pie-chart-card">
                <div class="chart" ref="pieChart"></div>
              </div>

              <!-- 中国地图 -->
              <div class="card map-card">
                <h3 class="card-title">资产地域分布</h3>
                <div class="chart" ref="mapChart"></div>
              </div>

              <!-- REITs行业Cap Rate -->
              <div class="card bar-chart-card">
                <h3 class="card-title">行业平均Cap Rate</h3>
                <div class="chart" ref="barChart"></div>
              </div>
            </div>
          </div>

          <!-- 右侧信息面板 -->
          <div class="info-panel">
          <!-- 日历 -->
          <div class="card calendar-card">
            <h3 class="card-title">行业日历</h3>
            <div class="calendar-content">
              <div class="calendar-header">2023年7月</div>
              <div class="calendar-grid">
                <div class="calendar-day">1</div>
                <div class="calendar-day">2</div>
                <div class="calendar-day">3</div>
                <div class="calendar-day">4</div>
                <div class="calendar-day">5</div>
                <div class="calendar-day">6</div>
                <div class="calendar-day">7</div>
                <div class="calendar-day">8</div>
                <div class="calendar-day active">9</div>
                <div class="calendar-day">10</div>
                <div class="calendar-day">11</div>
                <div class="calendar-day highlight">12</div>
                <div class="calendar-day">13</div>
                <div class="calendar-day">14</div>
                <div class="calendar-day">15</div>
                <div class="calendar-day">16</div>
                <div class="calendar-day">17</div>
                <div class="calendar-day">18</div>
                <div class="calendar-day">19</div>
                <div class="calendar-day">20</div>
                <div class="calendar-day">21</div>
                <div class="calendar-day">22</div>
                <div class="calendar-day">23</div>
                <div class="calendar-day">24</div>
                <div class="calendar-day highlight">25</div>
                <div class="calendar-day">26</div>
                <div class="calendar-day">27</div>
                <div class="calendar-day">28</div>
                <div class="calendar-day">29</div>
                <div class="calendar-day">30</div>
                <div class="calendar-day">31</div>
              </div>
            </div>
          </div>

          <!-- 市场新闻 -->
          <div class="card news-card">
            <h3 class="card-title">市场新闻</h3>
            <div class="news-list">
              <div class="news-item">
                <div class="news-tag">科技</div>
                <div class="news-title">海外AI应用场景：2022上半年大趋势</div>
                <div class="news-meta">来源：中国证券报 2022-05-21</div>
              </div>
              <div class="news-item">
                <div class="news-tag">科技</div>
                <div class="news-title">2025上半年大趋势</div>
                <div class="news-meta">来源：中国证券报 2022-05-21</div>
              </div>
              <div class="news-item">
                <div class="news-tag">科技</div>
                <div class="news-title">海外AI应用场景：2022上半年大趋势</div>
                <div class="news-meta">来源：中国证券报 2022-05-21</div>
              </div>
              <div class="news-item">
                <div class="news-tag">科技</div>
                <div class="news-title">海外AI应用场景：2022上半年大趋势</div>
                <div class="news-meta">来源：中国证券报 2022-05-21</div>
              </div>
              <div class="news-item">
                <div class="news-tag">科技</div>
                <div class="news-title">海外AI应用场景：2022上半年大趋势</div>
                <div class="news-meta">来源：中国证券报 2022-05-21</div>
              </div>
              <div class="news-item">
                <div class="news-tag">科技</div>
                <div class="news-title">海外AI应用场景：2022上半年大趋势</div>
                <div class="news-meta">来源：中国证券报 2022-05-21</div>
              </div>
            </div>
          </div>
        </div>
        </div>

        <!-- 底部信息区域 -->
        <div class="bottom-info">
          <!-- 行业日历 -->
          <div class="card calendar-card">
            <h3 class="card-title">行业日历</h3>
            <div class="calendar-content">
              <div class="calendar-item">
                <div class="date">2024-01-15</div>
                <div class="event">REITs产品发行</div>
              </div>
              <div class="calendar-item">
                <div class="date">2024-01-20</div>
                <div class="event">行业政策发布</div>
              </div>
              <div class="calendar-item">
                <div class="date">2024-01-25</div>
                <div class="event">市场分析报告</div>
              </div>
            </div>
          </div>

          <!-- 市场新闻 -->
          <div class="card news-card">
            <h3 class="card-title">市场新闻</h3>
            <div class="news-list">
              <div class="news-item">
                <div class="news-title">公募REITs市场持续回暖</div>
                <div class="news-time">2024-01-15 10:30</div>
              </div>
              <div class="news-item">
                <div class="news-title">新政策支持REITs发展</div>
                <div class="news-time">2024-01-14 15:20</div>
              </div>
              <div class="news-item">
                <div class="news-title">基础设施REITs表现亮眼</div>
                <div class="news-time">2024-01-13 09:15</div>
              </div>
            </div>
          </div>

          <!-- 宏观数据 -->
          <div class="card macro-card">
            <h3 class="card-title">宏观数据</h3>
            <div class="macro-data">
              <div class="data-item">
                <span class="label">GDP增长率</span>
                <span class="value">5.2%</span>
              </div>
              <div class="data-item">
                <span class="label">CPI</span>
                <span class="value">2.1%</span>
              </div>
              <div class="data-item">
                <span class="label">利率</span>
                <span class="value">3.85%</span>
              </div>
            </div>
          </div>

          <!-- 行业平均Cap Rate -->
          <div class="card cap-rate-card">
            <h3 class="card-title">行业平均Cap Rate</h3>
            <div class="cap-rate-data">
              <div class="rate-item">
                <span class="sector">产业园区</span>
                <span class="rate">4.8%</span>
              </div>
              <div class="rate-item">
                <span class="sector">仓储物流</span>
                <span class="rate">5.2%</span>
              </div>
              <div class="rate-item">
                <span class="sector">基础设施</span>
                <span class="rate">4.5%</span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const marketChart = ref<HTMLElement>()
const pieChart = ref<HTMLElement>()
const mapChart = ref<HTMLElement>()
const barChart = ref<HTMLElement>()

onMounted(() => {
  // 初始化市场概览图表 - 多线图表
  if (marketChart.value) {
    const chart = echarts.init(marketChart.value)
    const option = {
      backgroundColor: 'rgba(255, 255, 255, 1)',
      grid: {
        left: '10%',
        right: '10%',
        top: '15%',
        bottom: '20%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['基础设施', '保障性租赁住房', '产业园区', '仓储物流', '消费基础设施', '清洁能源', '高速公路'],
        top: '5%',
        textStyle: {
          fontSize: 12
        }
      },
      xAxis: {
        type: 'category',
        data: ['2021-06-21', '2021-12-31', '2022-06-30', '2022-12-31', '2023-06-30', '2023-12-31'],
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          fontSize: 10,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '收益率(%)',
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      series: [
        {
          name: '基础设施',
          type: 'line',
          data: [2.5, 3.2, 2.8, 4.1, 3.5, 3.8],
          smooth: true,
          lineStyle: { color: '#1868F1', width: 2 }
        },
        {
          name: '保障性租赁住房',
          type: 'line',
          data: [1.8, 2.5, 2.1, 3.2, 2.8, 3.1],
          smooth: true,
          lineStyle: { color: '#52C41A', width: 2 }
        },
        {
          name: '产业园区',
          type: 'line',
          data: [3.1, 3.8, 3.4, 4.5, 4.1, 4.3],
          smooth: true,
          lineStyle: { color: '#FAAD14', width: 2 }
        },
        {
          name: '仓储物流',
          type: 'line',
          data: [2.2, 2.9, 2.5, 3.6, 3.2, 3.5],
          smooth: true,
          lineStyle: { color: '#F5222D', width: 2 }
        },
        {
          name: '消费基础设施',
          type: 'line',
          data: [1.5, 2.1, 1.8, 2.8, 2.4, 2.7],
          smooth: true,
          lineStyle: { color: '#722ED1', width: 2 }
        },
        {
          name: '清洁能源',
          type: 'line',
          data: [2.8, 3.5, 3.1, 4.2, 3.8, 4.1],
          smooth: true,
          lineStyle: { color: '#13C2C2', width: 2 }
        },
        {
          name: '高速公路',
          type: 'line',
          data: [2.0, 2.7, 2.3, 3.4, 3.0, 3.3],
          smooth: true,
          lineStyle: { color: '#EB2F96', width: 2 }
        }
      ]
    }
    chart.setOption(option)
  }

  // 初始化饼图 - 分行业市值占比
  if (pieChart.value) {
    const chart = echarts.init(pieChart.value)
    const option = {
      backgroundColor: 'rgba(255, 255, 255, 1)',
      title: {
        text: '分行业市值占比',
        left: 'center',
        top: '5%',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}% ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: '10%',
        top: 'center',
        data: ['产业园区', '仓储物流', '基础设施', '保障性租赁住房', '消费基础设施', '清洁能源', '高速公路', '其他'],
        textStyle: {
          fontSize: 12
        }
      },
      series: [{
        name: '分行业市值占比',
        type: 'pie',
        center: ['35%', '55%'],
        radius: ['30%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: '产业园区', itemStyle: { color: '#1868F1' } },
          { value: 25, name: '仓储物流', itemStyle: { color: '#52C41A' } },
          { value: 20, name: '基础设施', itemStyle: { color: '#FAAD14' } },
          { value: 8, name: '保障性租赁住房', itemStyle: { color: '#F5222D' } },
          { value: 5, name: '消费基础设施', itemStyle: { color: '#722ED1' } },
          { value: 4, name: '清洁能源', itemStyle: { color: '#13C2C2' } },
          { value: 2, name: '高速公路', itemStyle: { color: '#EB2F96' } },
          { value: 1, name: '其他', itemStyle: { color: '#8C8C8C' } }
        ]
      }]
    }
    chart.setOption(option)
  }

  // 初始化地图图表 - 使用散点图代替
  if (mapChart.value) {
    const chart = echarts.init(mapChart.value)
    const option = {
      backgroundColor: 'rgba(255, 255, 255, 1)',
      title: {
        text: '资产地域分布',
        left: 'center',
        top: '5%',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '20%',
        bottom: '10%'
      },
      xAxis: {
        type: 'value',
        show: false
      },
      yAxis: {
        type: 'value',
        show: false
      },
      series: [{
        name: '资产分布',
        type: 'scatter',
        symbolSize: function (val) {
          return val[2] * 8; // 根据数值调整气泡大小
        },
        data: [
          [116.4, 39.9, 35, '北京'],
          [121.5, 31.2, 28, '上海'],
          [113.3, 23.1, 22, '深圳'],
          [118.8, 32.0, 8, '南京'],
          [120.2, 30.3, 4, '杭州'],
          [117.0, 36.7, 3, '济南']
        ],
        label: {
          show: true,
          formatter: function(param) {
            return param.data[3] + '\n' + param.data[2] + '%';
          },
          position: 'top'
        },
        itemStyle: {
          color: function(param) {
            const colors = ['#1868F1', '#52C41A', '#FAAD14', '#F5222D', '#722ED1', '#13C2C2'];
            return colors[param.dataIndex % colors.length];
          }
        }
      }]
    }
    chart.setOption(option)
  }

  // 初始化横向柱状图 - Cap Rate
  if (barChart.value) {
    const chart = echarts.init(barChart.value)
    const option = {
      backgroundColor: 'rgba(255, 255, 255, 1)',
      title: {
        text: '行业平均Cap Rate',
        left: 'center',
        top: '5%',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '20%',
        right: '10%',
        top: '20%',
        bottom: '10%'
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      yAxis: {
        type: 'category',
        data: ['产业园区', '仓储物流', '基础设施', '保障性租赁住房', '消费基础设施', '清洁能源', '高速公路'],
        axisLabel: {
          fontSize: 12
        }
      },
      series: [{
        name: 'Cap Rate',
        type: 'bar',
        data: [
          { value: 5.4, itemStyle: { color: '#1868F1' } },
          { value: 4.8, itemStyle: { color: '#52C41A' } },
          { value: 4.2, itemStyle: { color: '#FAAD14' } },
          { value: 3.8, itemStyle: { color: '#F5222D' } },
          { value: 3.5, itemStyle: { color: '#722ED1' } },
          { value: 4.5, itemStyle: { color: '#13C2C2' } },
          { value: 4.1, itemStyle: { color: '#EB2F96' } }
        ],
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }]
    }
    chart.setOption(option)
  }
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background-color: rgba(242, 242, 242, 1);
  font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
  margin: 0;
  padding: 0;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  border: none;
  border-radius: 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 30px;
  height: 30px;
  background: #1868F1;
  border-radius: 50%;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1868F1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon-bell::before {
  content: '🔔';
  font-size: 20px;
}

.user-info {
  position: relative;
  cursor: pointer;
}

.username {
  font-size: 14px;
  color: #333;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: none;
  min-width: 120px;
}

.user-info:hover .user-dropdown {
  display: block;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.dropdown-item:hover {
  background: rgba(242, 242, 242, 1);
}

/* 主要内容区域 */
.main-content {
  display: flex;
  margin-top: 70px;
  min-height: calc(100vh - 70px);
}

/* 左侧菜单 */
.sidebar {
  width: 200px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 5px 0px 5px rgba(215, 215, 215, 0.34901960784313724);
  border: none;
  border-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 0px;
  padding-top: 20px;
}

.menu {
  display: flex;
  flex-direction: column;
}

.menu-item {
  padding: 15px 20px;
  font-size: 14px;
  color: #555555;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.3s;
  height: 50px;
  display: flex;
  align-items: center;
}

.menu-item:hover {
  background: rgba(242, 242, 242, 1);
  color: #555555;
}

.menu-item.active {
  background: rgba(242, 242, 242, 1);
  color: #555555;
  border-left-color: transparent;
}

/* 右侧内容区域 */
.content {
  flex: 1;
  padding: 20px;
  background: rgba(242, 242, 242, 1);
}

/* 内容布局 */
.content-layout {
  display: flex;
  gap: 20px;
  height: 100%;
}

/* 图表区域 */
.charts-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 信息面板 */
.info-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 市场概览 */
.market-overview {
  background: rgba(255, 255, 255, 1);
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  border: none;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.chart-wrapper {
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 数据卡片 */
.data-cards {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
  height: 400px;
}

.card {
  background: rgba(255, 255, 255, 1);
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0px 0px 10px rgba(85, 85, 85, 0.34901960784313724);
  border: 1px solid rgba(215, 215, 215, 1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

/* 排行榜 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.rank {
  width: 24px;
  height: 24px;
  background: #1868F1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.value {
  font-size: 14px;
  font-weight: 600;
  color: #1868F1;
}

/* 饼图卡片 */
.pie-chart-card .chart {
  height: 350px;
}

/* 地图卡片 */
.map-card .chart {
  height: 350px;
}

/* 柱状图卡片 */
.bar-chart-card .chart {
  height: 350px;
}

/* 日历样式 */
.calendar-card {
  height: 300px;
}

.calendar-header {
  text-align: center;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}

.calendar-day {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.calendar-day:hover {
  background: rgba(24, 104, 241, 0.1);
}

.calendar-day.active {
  background: #1868F1;
  color: white;
}

.calendar-day.highlight {
  background: rgba(24, 104, 241, 0.2);
  color: #1868F1;
  font-weight: 600;
}

/* 新闻样式 */
.news-card {
  flex: 1;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.news-item {
  padding: 12px;
  border-left: 3px solid #1868F1;
  background: rgba(24, 104, 241, 0.05);
  border-radius: 4px;
}

.news-tag {
  display: inline-block;
  background: #1868F1;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  margin-bottom: 8px;
}

.news-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.4;
}

.news-meta {
  font-size: 12px;
  color: #666;
}

/* 地域分布 */
.region-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.region-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.region {
  width: 60px;
  font-size: 14px;
  color: #333;
}

.progress-bar {
  flex: 1;
  height: 10px;
  background: rgba(215, 215, 215, 0.9921568627450981);
  border-radius: 0px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: rgba(24, 104, 241, 0.9921568627450981);
  border-radius: 0px;
  transition: width 0.3s;
}

.percentage {
  width: 40px;
  font-size: 14px;
  color: #666;
  text-align: right;
}

/* 底部信息区域 */
.bottom-info {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* 日历卡片 */
.calendar-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.calendar-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.date {
  font-size: 12px;
  color: #666;
}

.event {
  font-size: 14px;
  color: #333;
}

/* 新闻卡片 */
.news-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.news-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.news-time {
  font-size: 12px;
  color: #999;
}

/* 宏观数据 */
.macro-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  font-weight: 600;
  color: #1868F1;
}

/* Cap Rate */
.cap-rate-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.sector {
  font-size: 14px;
  color: #333;
}

.rate {
  font-size: 14px;
  font-weight: 600;
  color: #1868F1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    flex-direction: column;
  }

  .info-panel {
    width: 100%;
    flex-direction: row;
  }

  .data-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .bottom-info {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 160px;
  }

  .info-panel {
    flex-direction: column;
  }

  .data-cards {
    grid-template-columns: 1fr;
  }

  .bottom-info {
    grid-template-columns: 1fr;
  }
}
</style> 