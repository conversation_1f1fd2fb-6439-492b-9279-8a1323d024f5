﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="159px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="594px" y="223px" width="159px" height="79px" filterUnits="userSpaceOnUse" id="filter6">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget7">
      <path d="M 604 237  A 4 4 0 0 1 608 233 L 739 233  A 4 4 0 0 1 743 237 L 743 288  A 4 4 0 0 1 739 292 L 608 292  A 4 4 0 0 1 604 288 L 604 237  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -594 -223 )">
    <use xlink:href="#widget7" filter="url(#filter6)" />
    <use xlink:href="#widget7" />
  </g>
</svg>