﻿<!DOCTYPE html>
<html>
  <head>
    <title>基本信息-修改密码</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/基本信息-修改密码/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/基本信息-修改密码/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u3242" class="ax_default box_3">
        <div id="u3242_div" class=""></div>
        <div id="u3242_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3243" class="ax_default box_3">
        <div id="u3243_div" class=""></div>
        <div id="u3243_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3244" class="ax_default _三级标题">
        <div id="u3244_div" class=""></div>
        <div id="u3244_text" class="text ">
          <p><span>修改密码</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u3245" class="ax_default _图片">
        <img id="u3245_img" class="img " src="images/公募reits产品及资产-产品对比/u2006.png"/>
        <div id="u3245_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3246" class="ax_default box_2">
        <div id="u3246_div" class=""></div>
        <div id="u3246_text" class="text ">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3247" class="ax_default _三级标题">
        <div id="u3247_div" class=""></div>
        <div id="u3247_text" class="text ">
          <p><span>新&nbsp; 密&nbsp; 码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3248" class="ax_default box_2">
        <div id="u3248_div" class=""></div>
        <div id="u3248_text" class="text ">
          <p><span>请设置登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3249" class="ax_default _三级标题">
        <div id="u3249_div" class=""></div>
        <div id="u3249_text" class="text ">
          <p><span>确认密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3250" class="ax_default box_2">
        <div id="u3250_div" class=""></div>
        <div id="u3250_text" class="text ">
          <p><span>请再次确认登录密码</span></p>
        </div>
      </div>

      <!-- 预约 (组合) -->
      <div id="u3251" class="ax_default" data-label="预约" data-left="830" data-top="281" data-width="290" data-height="36" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u3252" class="ax_default _形状1">
          <div id="u3252_div" class=""></div>
          <div id="u3252_text" class="text ">
            <p><span>6-20个字符，需包含字母、数字，不能包含空格</span></p>
          </div>
        </div>

        <!-- Unnamed (Triangle Down) -->
        <div id="u3253" class="ax_default _形状1">
          <img id="u3253_img" class="img " src="images/注册/u84.svg"/>
          <div id="u3253_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u3254" class="ax_default _图片">
        <img id="u3254_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u3254_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u3255" class="ax_default _图片">
        <img id="u3255_img" class="img " src="images/登录-密码登录/u19.png"/>
        <div id="u3255_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u3256" class="ax_default ax_default_hidden" style="display:none; visibility: hidden" data-left="896" data-top="361" data-width="120" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u3257" class="ax_default _形状1">
          <div id="u3257_div" class=""></div>
          <div id="u3257_text" class="text ">
            <p><span>修改成功</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u3258" class="ax_default icon">
          <img id="u3258_img" class="img " src="images/基本信息-修改密码/u3258.svg"/>
          <div id="u3258_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
