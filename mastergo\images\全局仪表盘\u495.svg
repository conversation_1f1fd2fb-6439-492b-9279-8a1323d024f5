﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="207px" height="106px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="1053px" y="875px" width="207px" height="106px" filterUnits="userSpaceOnUse" id="filter11">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget12">
      <path d="M 1063 889  A 4 4 0 0 1 1067 885 L 1246 885  A 4 4 0 0 1 1250 889 L 1250 967  A 4 4 0 0 1 1246 971 L 1067 971  A 4 4 0 0 1 1063 967 L 1063 889  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -1053 -875 )">
    <use xlink:href="#widget12" filter="url(#filter11)" />
    <use xlink:href="#widget12" />
  </g>
</svg>