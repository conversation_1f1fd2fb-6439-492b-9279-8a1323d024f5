<template>
  <div class="home-container">
    <a-layout>
      <a-layout-header class="header">
        <div class="logo">REITS Project</div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          theme="dark"
          mode="horizontal"
          class="menu"
        >
          <a-menu-item key="1" @click="$router.push('/')">首页</a-menu-item>
          <a-menu-item key="2" @click="$router.push('/dashboard')">仪表板</a-menu-item>
          <a-menu-item key="3" @click="$router.push('/charts')">图表</a-menu-item>
          <a-menu-item key="4" @click="$router.push('/table')">表格</a-menu-item>
        </a-menu>
      </a-layout-header>
      
      <a-layout-content class="content">
        <div class="welcome-card">
          <a-card title="欢迎使用 REITS Project">
            <p>这是一个基于以下技术栈构建的现代化前端项目：</p>
            <a-divider />
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <a-card size="small" title="核心框架">
                  <a-tag color="green">Vue 3.0</a-tag>
                  <a-tag color="blue">TypeScript</a-tag>
                  <a-tag color="orange">Vite 6</a-tag>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card size="small" title="UI & 样式">
                  <a-tag color="cyan">Ant Design Vue 4</a-tag>
                  <a-tag color="purple">UnoCSS</a-tag>
                </a-card>
              </a-col>
              <a-col :span="8">
                <a-card size="small" title="状态管理 & 工具">
                  <a-tag color="geekblue">Pinia</a-tag>
                  <a-tag color="magenta">ECharts</a-tag>
                  <a-tag color="gold">VXE-Table</a-tag>
                </a-card>
              </a-col>
            </a-row>
            
            <a-divider />
            
            <div class="actions">
              <a-button type="primary" size="large" @click="handleStart">
                开始探索
              </a-button>
              <a-button size="large" @click="store.increment">
                计数器: {{ store.count }}
              </a-button>
            </div>
          </a-card>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMainStore } from '@/store'
import { message } from 'ant-design-vue'

const router = useRouter()
const store = useMainStore()
const selectedKeys = ref(['1'])

const handleStart = () => {
  message.success('欢迎使用 REITS Project!')
  router.push('/dashboard')
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.logo {
  color: white;
  font-size: 18px;
  font-weight: bold;
  margin-right: 40px;
}

.menu {
  flex: 1;
  border: none;
}

.content {
  padding: 24px;
  background: #f0f2f5;
}

.welcome-card {
  max-width: 1200px;
  margin: 0 auto;
}

.actions {
  text-align: center;
  margin-top: 20px;
}

.actions .ant-btn {
  margin: 0 8px;
}
</style> 