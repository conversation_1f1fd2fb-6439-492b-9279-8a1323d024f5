﻿<!DOCTYPE html>
<html>
  <head>
    <title>个人中心-基本信息</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/个人中心-基本信息/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/个人中心-基本信息/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u3168" class="ax_default box_2">
        <div id="u3168_div" class=""></div>
        <div id="u3168_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3169" class="ax_default box_3">
        <div id="u3169_div" class=""></div>
        <div id="u3169_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (圆形) -->
      <div id="u3170" class="ax_default ellipse">
        <img id="u3170_img" class="img " src="images/个人中心-基本信息/u3170.svg"/>
        <div id="u3170_text" class="text ">
          <p><span>头像</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3171" class="ax_default label1">
        <div id="u3171_div" class=""></div>
        <div id="u3171_text" class="text ">
          <p><span>个人信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3172" class="ax_default label1">
        <div id="u3172_div" class=""></div>
        <div id="u3172_text" class="text ">
          <p><span>用户名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3173" class="ax_default box_2">
        <div id="u3173_div" class=""></div>
        <div id="u3173_text" class="text ">
          <p><span>chsdufizj</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3174" class="ax_default label1">
        <div id="u3174_div" class=""></div>
        <div id="u3174_text" class="text ">
          <p><span>修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3175" class="ax_default label1">
        <div id="u3175_div" class=""></div>
        <div id="u3175_text" class="text ">
          <p><span>查看大图</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3176" class="ax_default label1">
        <div id="u3176_div" class=""></div>
        <div id="u3176_text" class="text ">
          <p><span>手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3177" class="ax_default box_2">
        <div id="u3177_div" class=""></div>
        <div id="u3177_text" class="text ">
          <p><span>17854956489</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3178" class="ax_default label1">
        <div id="u3178_div" class=""></div>
        <div id="u3178_text" class="text ">
          <p><span>修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3179" class="ax_default label1">
        <div id="u3179_div" class=""></div>
        <div id="u3179_text" class="text ">
          <p><span>密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3180" class="ax_default box_2">
        <div id="u3180_div" class=""></div>
        <div id="u3180_text" class="text ">
          <p><span>*************</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3181" class="ax_default label1">
        <div id="u3181_div" class=""></div>
        <div id="u3181_text" class="text ">
          <p><span>修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3182" class="ax_default label1">
        <div id="u3182_div" class=""></div>
        <div id="u3182_text" class="text ">
          <p><span>修改</span></p>
        </div>
      </div>

      <!-- Unnamed (个人中心页面菜单) -->

      <!-- Unnamed (矩形) -->
      <div id="u3184" class="ax_default box_2">
        <div id="u3184_div" class=""></div>
        <div id="u3184_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u3185" class="ax_default" data-left="1840" data-top="20" data-width="42" data-height="30" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u3186" class="ax_default _图片1">
          <img id="u3186_img" class="img " src="images/全局仪表盘/u122.svg"/>
          <div id="u3186_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u3187" class="ax_default icon">
          <img id="u3187_img" class="img " src="images/全局仪表盘/u123.svg"/>
          <div id="u3187_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u3188" class="ax_default" data-left="30" data-top="20" data-width="113" data-height="30" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u3189" class="ax_default _三级标题">
          <div id="u3189_div" class=""></div>
          <div id="u3189_text" class="text ">
            <p><span>rev-REITs</span></p>
          </div>
        </div>

        <!-- Unnamed (圆形) -->
        <div id="u3190" class="ax_default ellipse">
          <img id="u3190_img" class="img " src="images/全局仪表盘/u126.svg"/>
          <div id="u3190_text" class="text ">
            <p><span>logo</span></p>
          </div>
        </div>
      </div>

      <!-- 用户 (组合) -->
      <div id="u3191" class="ax_default ax_default_hidden" data-label="用户" style="display:none; visibility: hidden" data-left="1762" data-top="59" data-width="150" data-height="153" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u3192" class="ax_default box_2">
          <div id="u3192_div" class=""></div>
          <div id="u3192_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3193" class="ax_default _段落">
          <div id="u3193_div" class=""></div>
          <div id="u3193_text" class="text ">
            <p><span>用户名</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3194" class="ax_default _段落">
          <div id="u3194_div" class=""></div>
          <div id="u3194_text" class="text ">
            <p><span>个人中心</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3195" class="ax_default _段落">
          <div id="u3195_div" class=""></div>
          <div id="u3195_text" class="text ">
            <p><span>退出登录</span></p>
          </div>
        </div>

        <!-- Unnamed (直线) -->
        <div id="u3196" class="ax_default line">
          <img id="u3196_img" class="img " src="images/全局仪表盘/u132.svg"/>
          <div id="u3196_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
      <div id="u3183" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u3197" class="ax_default label1">
        <div id="u3197_div" class=""></div>
        <div id="u3197_text" class="text ">
          <p><span>头像</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3198" class="ax_default box_2">
        <div id="u3198_div" class=""></div>
        <div id="u3198_text" class="text ">
          <p><span>返回</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3199" class="ax_default sticky_1">
        <div id="u3199_div" class=""></div>
        <div id="u3199_text" class="text ">
          <p><span>如果是通过中国REITs论坛账号登录，手机号密码不能修改，按钮置灰</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
