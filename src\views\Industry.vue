<template>
  <div class="industry-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon"></div>
          <div class="logo-text">rev-REITs</div>
        </div>
      </div>
      <div class="header-right">
        <div class="notification-icon">
          <i class="icon-bell"></i>
        </div>
        <div class="user-info">
          <span class="username">用户名</span>
          <div class="user-dropdown">
            <div class="dropdown-item">个人中心</div>
            <div class="dropdown-item">退出登录</div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单 -->
      <aside class="sidebar">
        <nav class="menu">
          <div class="menu-item" @click="navigateTo('/dashboard')">
            <span>全局仪表盘</span>
          </div>
          <div class="menu-item active">
            <span>行业情况展示</span>
          </div>
          <div class="menu-item" @click="navigateTo('/products')">
            <span>公募REITs产品及资产</span>
          </div>
          <div class="menu-item" @click="navigateTo('/market')">
            <span>市场动态</span>
          </div>
        </nav>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="content">
        <!-- 行业选择器 -->
        <section class="industry-selector">
          <h2 class="section-title">选择行业</h2>
          <div class="industry-tabs">
            <div 
              v-for="industry in industries" 
              :key="industry.id"
              class="industry-tab"
              :class="{ active: selectedIndustry === industry.id }"
              @click="selectIndustry(industry.id)"
            >
              {{ industry.name }}
            </div>
          </div>
        </section>

        <!-- 核心数据看板 -->
        <section class="core-data">
          <h2 class="section-title">核心数据看板</h2>
          <div class="data-grid">
            <div class="data-card">
              <div class="data-header">
                <span class="data-label">总资产规模</span>
                <div class="data-icon">📊</div>
              </div>
              <div class="data-value">{{ currentIndustryData.totalAssets }}</div>
              <div class="data-unit">亿元</div>
            </div>
            <div class="data-card">
              <div class="data-header">
                <span class="data-label">平均收益率</span>
                <div class="data-icon">📈</div>
              </div>
              <div class="data-value">{{ currentIndustryData.avgReturn }}</div>
              <div class="data-unit">%</div>
            </div>
            <div class="data-card">
              <div class="data-header">
                <span class="data-label">产品数量</span>
                <div class="data-icon">🏢</div>
              </div>
              <div class="data-value">{{ currentIndustryData.productCount }}</div>
              <div class="data-unit">个</div>
            </div>
            <div class="data-card">
              <div class="data-header">
                <span class="data-label">市值占比</span>
                <div class="data-icon">💰</div>
              </div>
              <div class="data-value">{{ currentIndustryData.marketShare }}</div>
              <div class="data-unit">%</div>
            </div>
          </div>
        </section>

        <!-- 图表区域 -->
        <div class="charts-container">
          <!-- 供需平衡 -->
          <section class="chart-section">
            <h3 class="chart-title">供需平衡</h3>
            <div class="chart-wrapper">
              <div class="chart" ref="supplyDemandChart"></div>
            </div>
          </section>

          <!-- 价格趋势 -->
          <section class="chart-section">
            <h3 class="chart-title">价格趋势</h3>
            <div class="chart-wrapper">
              <div class="chart" ref="priceTrendChart"></div>
            </div>
          </section>
        </div>

        <!-- 行业详情 -->
        <section class="industry-details">
          <h2 class="section-title">行业详情</h2>
          <div class="details-grid">
            <div class="detail-card">
              <h4 class="detail-title">主要特点</h4>
              <ul class="detail-list">
                <li v-for="feature in currentIndustryData.features" :key="feature">{{ feature }}</li>
              </ul>
            </div>
            <div class="detail-card">
              <h4 class="detail-title">风险因素</h4>
              <ul class="detail-list">
                <li v-for="risk in currentIndustryData.risks" :key="risk">{{ risk }}</li>
              </ul>
            </div>
            <div class="detail-card">
              <h4 class="detail-title">发展前景</h4>
              <p class="detail-text">{{ currentIndustryData.prospects }}</p>
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()

// 行业数据
const industries = ref([
  { id: 'park', name: '园区基础设施' },
  { id: 'warehouse', name: '仓储物流基础设施' },
  { id: 'consumption', name: '消费基础设施' },
  { id: 'housing', name: '租赁住房' },
  { id: 'energy', name: '能源基础设施' },
  { id: 'highway', name: '高速公路' }
])

const selectedIndustry = ref('park')

// 行业详细数据
const industryData = ref({
  park: {
    totalAssets: '1,250.6',
    avgReturn: '6.8',
    productCount: '86',
    marketShare: '35.2',
    features: [
      '产业集聚效应明显',
      '租金收入稳定',
      '政策支持力度大',
      '市场需求持续增长'
    ],
    risks: [
      '经济周期影响较大',
      '区域竞争激烈',
      '政策变化风险'
    ],
    prospects: '随着产业升级和数字化转型，园区基础设施REITs将迎来新的发展机遇，预计未来3-5年市场规模将保持15%以上的年增长率。'
  },
  warehouse: {
    totalAssets: '980.3',
    avgReturn: '5.9',
    productCount: '45',
    marketShare: '28.1',
    features: [
      '电商物流需求旺盛',
      '地理位置优势明显',
      '运营效率高',
      '现金流稳定'
    ],
    risks: [
      '电商平台集中度风险',
      '物流技术变革影响',
      '土地成本上升'
    ],
    prospects: '受益于电商发展和供应链优化，仓储物流基础设施REITs具有长期增长潜力，预计未来将保持稳定增长。'
  },
  consumption: {
    totalAssets: '750.2',
    avgReturn: '7.2',
    productCount: '32',
    marketShare: '21.5',
    features: [
      '消费升级驱动',
      '品牌效应明显',
      '租金增长潜力大',
      '抗周期能力强'
    ],
    risks: [
      '消费习惯变化',
      '线上零售冲击',
      '品牌风险'
    ],
    prospects: '随着消费升级和新零售发展，消费基础设施REITs将受益于消费市场的持续增长。'
  },
  housing: {
    totalAssets: '520.8',
    avgReturn: '4.8',
    productCount: '28',
    marketShare: '15.0',
    features: [
      '政策支持明确',
      '需求刚性较强',
      '租金收入稳定',
      '社会效益显著'
    ],
    risks: [
      '政策变化风险',
      '租金管制影响',
      '运营成本上升'
    ],
    prospects: '在国家住房保障政策支持下，租赁住房REITs将获得更多发展机会。'
  },
  energy: {
    totalAssets: '380.5',
    avgReturn: '8.1',
    productCount: '18',
    marketShare: '10.9',
    features: [
      '能源需求稳定',
      '政策支持力度大',
      '技术门槛较高',
      '长期收益稳定'
    ],
    risks: [
      '能源政策变化',
      '技术更新风险',
      '环保要求提高'
    ],
    prospects: '在碳中和政策推动下，能源基础设施REITs将迎来新的发展机遇。'
  },
  highway: {
    totalAssets: '290.3',
    avgReturn: '6.5',
    productCount: '15',
    marketShare: '8.3',
    features: [
      '收费收入稳定',
      '地理位置重要',
      '运营成本可控',
      '现金流充沛'
    ],
    risks: [
      '交通政策变化',
      '替代交通方式',
      '维护成本上升'
    ],
    prospects: '随着交通网络完善和出行需求增长，高速公路REITs将保持稳定发展。'
  }
})

// 计算当前行业数据
const currentIndustryData = computed(() => {
  return industryData.value[selectedIndustry.value as keyof typeof industryData.value]
})

// 图表引用
const supplyDemandChart = ref<HTMLElement>()
const priceTrendChart = ref<HTMLElement>()

// 选择行业
const selectIndustry = (industryId: string) => {
  selectedIndustry.value = industryId
  // 重新初始化图表
  nextTick(() => {
    initSupplyDemandChart()
    initPriceTrendChart()
  })
}

// 导航功能
const navigateTo = (path: string) => {
  router.push(path)
}

// 初始化供需平衡图表
const initSupplyDemandChart = () => {
  if (!supplyDemandChart.value) return
  
  const chart = echarts.init(supplyDemandChart.value)
  const option = {
    title: {
      text: '供需平衡分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['供应量', '需求量'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['2020', '2021', '2022', '2023', '2024']
    },
    yAxis: {
      type: 'value',
      name: '规模（亿元）'
    },
    series: [
      {
        name: '供应量',
        type: 'bar',
        data: [120, 150, 180, 220, 260],
        itemStyle: {
          color: '#1868F1'
        }
      },
      {
        name: '需求量',
        type: 'bar',
        data: [100, 140, 170, 210, 250],
        itemStyle: {
          color: '#52C41A'
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化价格趋势图表
const initPriceTrendChart = () => {
  if (!priceTrendChart.value) return
  
  const chart = echarts.init(priceTrendChart.value)
  const option = {
    title: {
      text: '价格趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平均价格', '最高价格', '最低价格'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['2020', '2021', '2022', '2023', '2024']
    },
    yAxis: {
      type: 'value',
      name: '价格指数'
    },
    series: [
      {
        name: '平均价格',
        type: 'line',
        data: [100, 105, 112, 118, 125],
        smooth: true,
        lineStyle: {
          color: '#1868F1',
          width: 3
        }
      },
      {
        name: '最高价格',
        type: 'line',
        data: [110, 115, 122, 128, 135],
        smooth: true,
        lineStyle: {
          color: '#F5222D',
          width: 2
        }
      },
      {
        name: '最低价格',
        type: 'line',
        data: [90, 95, 102, 108, 115],
        smooth: true,
        lineStyle: {
          color: '#52C41A',
          width: 2
        }
      }
    ]
  }
  chart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    initSupplyDemandChart()
    initPriceTrendChart()
  })
})
</script>

<style scoped>
.industry-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 30px;
  height: 30px;
  background: #1868F1;
  border-radius: 50%;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1868F1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon-bell::before {
  content: '🔔';
  font-size: 20px;
}

.user-info {
  position: relative;
  cursor: pointer;
}

.username {
  font-size: 14px;
  color: #333;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: none;
  min-width: 120px;
}

.user-info:hover .user-dropdown {
  display: block;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.dropdown-item:hover {
  background: #f5f5f5;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  margin-top: 60px;
  min-height: calc(100vh - 60px);
}

/* 左侧菜单 */
.sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  padding-top: 20px;
}

.menu {
  display: flex;
  flex-direction: column;
}

.menu-item {
  padding: 15px 20px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.3s;
}

.menu-item:hover {
  background: #f5f5f5;
  color: #1868F1;
}

.menu-item.active {
  background: #f0f7ff;
  color: #1868F1;
  border-left-color: #1868F1;
}

/* 右侧内容区域 */
.content {
  flex: 1;
  padding: 20px;
  background: #f5f5f5;
}

/* 行业选择器 */
.industry-selector {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.industry-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.industry-tab {
  padding: 10px 20px;
  background: #f5f5f5;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.industry-tab:hover {
  background: #e6f3ff;
  color: #1868F1;
}

.industry-tab.active {
  background: #1868F1;
  color: white;
  border-color: #1868F1;
}

/* 核心数据看板 */
.core-data {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.data-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #1868F1;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.data-label {
  font-size: 14px;
  color: #666;
}

.data-icon {
  font-size: 20px;
}

.data-value {
  font-size: 28px;
  font-weight: 700;
  color: #1868F1;
  margin-bottom: 5px;
}

.data-unit {
  font-size: 12px;
  color: #999;
}

/* 图表区域 */
.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.chart-wrapper {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 行业详情 */
.industry-details {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.detail-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.detail-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.detail-list li {
  padding: 8px 0;
  font-size: 14px;
  color: #666;
  border-bottom: 1px solid #eee;
}

.detail-list li:last-child {
  border-bottom: none;
}

.detail-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-container {
    grid-template-columns: 1fr;
  }
  
  .details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 160px;
  }
  
  .data-grid {
    grid-template-columns: 1fr;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .industry-tabs {
    flex-direction: column;
  }
}
</style> 