﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="222px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="1361px" y="393px" width="222px" height="79px" filterUnits="userSpaceOnUse" id="filter17">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget18">
      <path d="M 1371 407  A 4 4 0 0 1 1375 403 L 1569 403  A 4 4 0 0 1 1573 407 L 1573 458  A 4 4 0 0 1 1569 462 L 1375 462  A 4 4 0 0 1 1371 458 L 1371 407  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -1361 -393 )">
    <use xlink:href="#widget18" filter="url(#filter17)" />
    <use xlink:href="#widget18" />
  </g>
</svg>