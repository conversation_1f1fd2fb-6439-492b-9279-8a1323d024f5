﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="94px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="409px" y="895px" width="94px" height="79px" filterUnits="userSpaceOnUse" id="filter30">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget31">
      <path d="M 419 909  A 4 4 0 0 1 423 905 L 489 905  A 4 4 0 0 1 493 909 L 493 960  A 4 4 0 0 1 489 964 L 423 964  A 4 4 0 0 1 419 960 L 419 909  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -409 -895 )">
    <use xlink:href="#widget31" filter="url(#filter30)" />
    <use xlink:href="#widget31" />
  </g>
</svg>