﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="180px" height="79px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="450px" y="538px" width="180px" height="79px" filterUnits="userSpaceOnUse" id="filter28">
      <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.2 0  " in="shadowComposite" />
    </filter>
    <g id="widget29">
      <path d="M 460 552  A 4 4 0 0 1 464 548 L 616 548  A 4 4 0 0 1 620 552 L 620 603  A 4 4 0 0 1 616 607 L 464 607  A 4 4 0 0 1 460 603 L 460 552  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0.8" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -450 -538 )">
    <use xlink:href="#widget29" filter="url(#filter28)" />
    <use xlink:href="#widget29" />
  </g>
</svg>